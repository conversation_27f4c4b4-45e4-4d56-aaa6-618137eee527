org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.parallel=true
org.gradle.caching=true

# Android configuration
android.useAndroidX=true
android.enableJetifier=true
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# Kotlin configuration
kotlin.code.style=official
kotlin.incremental=true
kotlin.stdlib.default.dependency=false

# Suppress Java compiler warnings for target compatibility
android.javaCompile.suppressSourceTargetDeprecationWarning=true

# Specific Kotlin version - must match build.gradle.kts
kotlinVersion=2.1.0
