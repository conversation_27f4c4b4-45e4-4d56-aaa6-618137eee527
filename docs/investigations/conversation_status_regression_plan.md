# Investigation Plan: Conversation/Agent Work Status Regression on Existing Session Login

## Objective
Identify and fix the regression causing failures or delays when fetching conversation and agent work statuses upon login when a prior session exists. Compare the current branch against known-good commit `e41cd4c9b2d155b846e94bfd94606c1f05a69ad1` and validate hypotheses with targeted experiments.

## Scope
- App start and login flows when credentials/session are already persisted
- Conversation list/status fetch and agent work status retrieval
- Auth token lifecycle across isolates and DI initialization
- Shim service vs Salesforce endpoints and client usage

## Critical Context
- Token types must be used correctly:
  - Salesforce `accessToken` for Salesforce API/GraphQL
  - Shim-service `authorizationToken` for shim-service endpoints
- Prior issues included:
  - Path normalization bug in `ShimServiceApiClient` stripping `/shim-service/` prefix
  - DI registration ordering causing `ShimServiceApiClient` not to be available/used as intended
  - Fix pattern: obtain shim `authorizationToken` via `/shim-service/auth/salesforce/obtain-token` using Salesforce `accessToken` + `orgId`; then use `authorizationToken` for all shim calls

## Success Criteria
- On cold start with an existing session, conversation and agent work statuses load successfully on the first attempt without manual refresh or re-login.
- Network traces show correct endpoints, headers, and tokens used.
- Automated tests for this flow pass reliably.

---

## Hypotheses (Ranked)
1. Token lifecycle on rehydration is incorrect
   - Stale/expired shim `authorizationToken` reused on app start, not refreshed
   - Salesforce `accessToken` mistakenly used for shim-service requests
2. Endpoint path normalization regression
   - `/shim-service/` prefix is stripped or double-appended only in app-start flow
3. DI/init sequencing or race condition
   - `ShimServiceApiClient` or interceptors not registered before network isolate issues initial fetches
   - Fetch happens before obtaining/refreshing shim token; result cached as failure
4. Persisted credential schema or TTL changed
   - Storage read yields incomplete creds; expiration not checked or misinterpreted
5. Request construction diverges between rehydrated and fresh-login flows
   - Missing `Authorization: Bearer <shim token>` header, wrong Accept/Content-Type, or orgId header missing
6. Environment/base URL misconfiguration
   - Wrong base URL for shim-service at startup vs post-login
7. Interceptor routing differences (web/extension vs mobile)
   - GraphQL or HTTP interceptors alter behavior for rehydrated sessions
8. Caching/ETag/304 handling regression
   - Startup requests incorrectly send cache validators leading to empty or blocked responses
9. Feature flag/config changes
   - `lib/config/*.json` changed keys that affect startup behavior
10. Platform-specific behavior
   - iOS/Android lifecycle differences on cold/warm start affecting init order

---

## Initial Findings (2025-08-27)
- High-volume changes in `lib/api/dtos/` including `presence_status*`, `sessions*`, `messaging*`, and token-related DTOs. The diffs show json_serializable/freezed codegen adjustments (e.g., `$checkedConvert` adoption, optional vs required field shifts) which can alter deserialization/validation behavior.
- Sensitive symbols in diffs highlight many `accessToken` changes across `SalesforceTokenBody`, `SalesforceTokenResponse`, and `SessionsBody`. Risk: token field optionality/validation changed, affecting startup rehydration and obtain-token payloads.
- Action from findings: prioritize verification of token and session DTO schemas and their persistence/rehydration flows before network calls. Add temporary logging to capture token presence/expiry and headers on first shim-service calls.

## Plan of Action

### 1) Establish Baseline Diffs vs Known-Good Commit
Commands:
```bash
# Ensure the commit exists locally
git fetch --all

# Quick overview of all changes since known-good
git diff --stat e41cd4c9b2d155b846e94bfd94606c1f05a69ad1...HEAD

# Focused diff on relevant areas
git diff e41cd4c9b2d155b846e94bfd94606c1f05a69ad1...HEAD -- \
  lib/api/ \
  lib/di/ \
  lib/conversations/ \
  lib/api/isolate/ \
  lib/api/converters/ \
  lib/config/ \
  test/conversations/ \
  test/channels/ \
  test/whatsapp_outbound/

# Search for sensitive symbols that often regress
git diff e41cd4c9b2d155b846e94bfd94606c1f05a69ad1...HEAD | grep -nE "(ShimServiceApiClient|authorizationToken|accessToken|/shim-service/|obtain-token|GraphQl|interceptor|NetworkServiceFactory)"
```
Artifacts to capture in this doc:
- Diff snippets impacting auth, endpoints, DI, isolates, and conversation retrieval

### 2) Reproduce With Instrumentation
- Start app with existing session:
  1) Log in once; fully close app
  2) Relaunch; observe first fetch of conversations and agent work status
- Capture logs:
  - Flutter run logs with verbose network logging
  - Instrument temporary logging if needed:
    - `ShimServiceApiClient._sendRequest()`: log method, full path, headers (sanitized), response code
    - Token acquisition flow: log when shim token is obtained/refreshed and TTL
  - Preserve logs in `debug/` as artifacts

### 3) Verify Token Handling
- On startup, confirm the sequence:
  - Read persisted creds: `accessToken`, `orgId`, stored `authorizationToken` (+ expiry)
  - If shim token missing/expired: call `/shim-service/auth/salesforce/obtain-token`
  - Ensure subsequent shim requests include `Authorization: Bearer <authorizationToken>`
- Validate no cross-use of tokens between Salesforce and shim-service clients

### 4) Audit DI and Isolate Init Order
- Trace `lib/di/di.dart`, `lib/di/modules/*`, `lib/api/isolate/network_isolate_integration.dart`
- Confirm registration of:
  - `NetworkServiceFactory.getShimServiceApi()` usage instead of direct `GetIt.I<ShimServiceApiClient>()`
  - Interceptors that attach tokens
  - Startup sequence doesn’t trigger fetch before token availability

### 5) Endpoints, Headers, Payloads
- Compare actual runtime requests for startup vs post-login fetch
- Confirm:
  - Endpoint paths contain `/shim-service/` when required
  - No double slashes; no stripped prefixes
  - Headers: `Authorization` present and correct; content-type as expected

### 6) Persistence and TTL
- Inspect credential storage/rehydration code for schema and TTL handling
- Validate token freshness checks on app start

### 7) Tests
Commands:
```bash
flutter test test/conversations/ -r expanded
flutter test test/channels/ -r expanded
flutter test test/whatsapp_outbound/ -r expanded
```
- If missing, add targeted tests for:
  - Existing-session startup path: ensure token refresh and initial fetch succeed
  - DI/init ordering: mocks that fail if fetch occurs pre-registration

### 8) Platform Cross-Check
- Verify behavior on Android and iOS at minimum
- If relevant, validate web/extension paths do not interfere with mobile init

### 9) Validate/Invalidate Hypotheses
- For each hypothesis, record:
  - Evidence (logs/diffs/tests)
  - Status: Validated / Invalidated / Inconclusive
  - Next action if validated

---

## Execution Checklist
- [ ] Generate and review focused diffs vs `e41cd4...`
- [ ] Reproduce with existing session and collect logs
- [ ] Verify token acquisition/refresh on startup
- [ ] Confirm endpoint paths and headers are correct
- [ ] Audit DI and isolate sequencing
- [ ] Review persistence schema and TTL logic
- [ ] Run and extend tests for startup with existing session
- [ ] Cross-check on Android and iOS
- [ ] Update this doc with findings and decisions

---

## Data to Capture
- Redacted HTTP traces: method, URL path, response code, timing
- Token lifecycle events: acquisition, expiry, refresh decisions
- DI init order timeline
- Any exceptions or 403/401 responses

---

## Decision Log (update as we investigate)
- 2025-08-27: Plan created. Baseline established. Pending diffs and reproduction.

Format for entries:
```
[YYYY-MM-DD] [Area] Finding -> Impact -> Action
```

---

## Owners
- Primary: Engineering (this branch maintainer)
- Support: Mobile + Backend (shim-service owners)

## Risks & Mitigations
- Risk: Debug logging exposes sensitive tokens -> Mitigation: sanitize logs and keep local
- Risk: Flaky reproduction -> Mitigation: write deterministic startup tests

---

## Appendix: Command Reference
```bash
# High-level changes since known-good commit
git diff --name-status e41cd4c9b2d155b846e94bfd94606c1f05a69ad1...HEAD

# Only Dart sources
git diff e41cd4c9b2d155b846e94bfd94606c1f05a69ad1...HEAD -- "**/*.dart"

# Grep for sensitive symbols in current branch
rg -n "(ShimServiceApiClient|authorizationToken|accessToken|/shim-service/|obtain-token|GraphQl|interceptor|NetworkServiceFactory)" lib/
```
