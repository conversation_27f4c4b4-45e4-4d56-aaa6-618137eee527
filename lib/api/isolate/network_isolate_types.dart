import 'dart:convert';

/// Enum representing the different API types
enum ApiType {
  shimService,
  salesforce;
  
  String get name {
    switch (this) {
      case ApiType.shimService:
        return 'shimService';
      case ApiType.salesforce:
        return 'salesforce';
    }
  }
  
  static ApiType fromString(String value) {
    return ApiType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => throw ArgumentError('Invalid ApiType: $value'),
    );
  }
}

/// Base class for all network messages
abstract class NetworkMessage {
  final String id;
  
  NetworkMessage({required this.id});
  
  Map<String, dynamic> toMap();
  
  /// Convert to a json string for passing between isolates
  String toJson() => jsonEncode(toMap());
}

/// Request sent from main isolate to network isolate
class NetworkRequest extends NetworkMessage {
  final ApiType apiType;
  final String method;
  final Map<String, dynamic> parameters;
  final Map<String, String>? headers;
  final String? authToken;
  final String? sessionToken;
  
  NetworkRequest({
    required String id,
    required this.apiType,
    required this.method,
    required this.parameters,
    this.headers,
    this.authToken,
    this.sessionToken,
  }) : super(id: id);
  
  @override
  Map<String, dynamic> toMap() => {
    'id': id,
    'apiType': apiType.name,
    'method': method,
    'parameters': parameters,
    'headers': headers,
    'authToken': authToken,
    'sessionToken': sessionToken,
  };
  
  static NetworkRequest fromMap(Map<String, dynamic> map) {
    // Handle parameters safely - it might be a JSON string or a Map
    Map<String, dynamic> parameters;
    final parametersRaw = map['parameters'];

    if (parametersRaw == null) {
      parameters = {};
    } else if (parametersRaw is Map<String, dynamic>) {
      parameters = parametersRaw;
    } else if (parametersRaw is Map) {
      parameters = Map<String, dynamic>.from(parametersRaw);
    } else if (parametersRaw is String) {
      try {
        final decoded = jsonDecode(parametersRaw);
        if (decoded is Map<String, dynamic>) {
          parameters = decoded;
        } else if (decoded is Map) {
          parameters = Map<String, dynamic>.from(decoded);
        } else {
          throw Exception('Decoded parameters is not a Map: ${decoded.runtimeType}');
        }
      } catch (e) {
        print('⚠️ NetworkRequest.fromMap - Failed to parse parameters JSON: $e');
        parameters = {};
      }
    } else {
      print('⚠️ NetworkRequest.fromMap - Unexpected parameters type: ${parametersRaw.runtimeType}');
      parameters = {};
    }

    return NetworkRequest(
      id: map['id'],
      apiType: ApiType.fromString(map['apiType']),
      method: map['method'],
      parameters: parameters,
      headers: map['headers'] != null ? Map<String, String>.from(map['headers']) : null,
      authToken: map['authToken'],
      sessionToken: map['sessionToken'],
    );
  }
  
  static NetworkRequest fromJson(String json) => fromMap(jsonDecode(json));
}

/// Response sent from network isolate back to main isolate
class NetworkResponse extends NetworkMessage {
  final bool success;
  final dynamic data;
  final String? error;
  final int? statusCode;
  final Map<String, dynamic>? metadata;
  
  NetworkResponse({
    required String id,
    required this.success,
    this.data,
    this.error,
    this.statusCode,
    this.metadata,
  }) : super(id: id);
  
  @override
  Map<String, dynamic> toMap() => {
    'id': id,
    'success': success,
    'data': data,
    'error': error,
    'statusCode': statusCode,
    'metadata': metadata,
  };
  
  static NetworkResponse fromMap(Map<String, dynamic> map) => NetworkResponse(
    id: map['id'],
    success: map['success'],
    data: map['data'],
    error: map['error'],
    statusCode: map['statusCode'],
    metadata: map['metadata'],
  );
  
  static NetworkResponse fromJson(String json) => fromMap(jsonDecode(json));
}

/// Special message for updating authentication info in the isolate
class AuthUpdateMessage extends NetworkMessage {
  final String? authToken;
  final String? sessionToken;
  
  AuthUpdateMessage({
    required String id,
    this.authToken,
    this.sessionToken,
  }) : super(id: id);
  
  @override
  Map<String, dynamic> toMap() => {
    'id': id,
    'type': 'auth_update',
    'authToken': authToken,
    'sessionToken': sessionToken,
  };
  
  static AuthUpdateMessage fromMap(Map<String, dynamic> map) => AuthUpdateMessage(
    id: map['id'],
    authToken: map['authToken'],
    sessionToken: map['sessionToken'],
  );
  
  static AuthUpdateMessage fromJson(String json) => fromMap(jsonDecode(json));
}
