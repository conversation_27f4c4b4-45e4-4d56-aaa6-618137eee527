import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:x1440/api/isolate/network_isolate_integration.dart';
import 'package:x1440/di/background_dependency_loader.dart';
import 'package:x1440/di/modules/background_initialized_module.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/utils/performance_monitor.dart';

import 'di.config.dart';

/// Defines initialization stages to optimize app startup
/// especially when launched from notifications
enum InitializationStage {
  minimal, // Only what's needed to handle a notification
  essential, // Core services required for basic UI
  complete // All services and features
}

@InjectableInit(
  generateForDir: ['lib/di'],
)
/// This class is used to hold initialization parameters for compute isolate
class _IsolateInitParams {
  final InitializationStage stage;
  final String environment;

  _IsolateInitParams({
    required this.stage,
    required this.environment,
  });
}

/// Result from background initialization
class _IsolateInitResult {
  final bool success;
  final String? error;
  final int durationMs;
  final bool nonUiDependenciesRegistered;

  _IsolateInitResult({
    required this.success,
    this.error,
    required this.durationMs,
    this.nonUiDependenciesRegistered = false,
  });
}

/// Initializes dependencies in a background isolate to prevent ANR warnings
/// This wrapper function manages the isolate and ensures UI is not blocked
Future<GetIt> configureDependenciesInBackground({
  InitializationStage stage = InitializationStage.complete,
}) async {
  final getIt = GetIt.instance;
  final beforeConfigDeps = DateTime.now();
  
  // For minimal initialization, we still do it on the main thread
  // since it's quick and needed immediately for notifications
  if (stage == InitializationStage.minimal) {
    return await configureDependencies(stage: stage);
  }

  // BackgroundDependencyLoader should already be registered in main.dart
  // but verify it exists, don't create a new instance
  if (!getIt.isRegistered<BackgroundDependencyLoader>()) {
    getIt.registerSingleton<BackgroundDependencyLoader>(BackgroundDependencyLoader());
  }

  try {
    // Start background dependency initialization early
    final backgroundLoader = getIt<BackgroundDependencyLoader>();
    final backgroundInitFuture = backgroundLoader.initializeBackgroundDependencies();

    // Prepare parameters for background initialization
    final params = _IsolateInitParams(
      stage: stage,
      environment: Utils.env,
    );

    // Run the heavy initialization work in a background isolate
    final result = await compute(_isolateInitDependencies, params);

    // Wait for background dependency initialization to complete
    final backgroundResult = await backgroundInitFuture;

    // Check BOTH results before determining success
    final bool computeSuccess = result.success;
    final bool backgroundSuccess = backgroundResult.success || backgroundResult.initializedDependencyData.isNotEmpty;

    final wasSuccessful = backgroundLoader.wasInitializationSuccessful;

    if (computeSuccess && backgroundSuccess) {
      return getIt;
    } else {
      return await configureDependencies(stage: stage);
    }
  } catch (e, stackTrace) {
    return await configureDependencies(stage: stage);
  }
}

/// Function that runs in the isolate to initialize dependencies
/// Cannot access UI-related functionality from here
Future<_IsolateInitResult> _isolateInitDependencies(_IsolateInitParams params) async {
  try {
    print('[ISOLATE] Starting dependency initialization - UI-free version');
    final startTime = DateTime.now();
    final getIt = GetIt.instance;
    
    // Register non-UI dependent services only
    try {
      // Initialize dependency injection with a special environment flag to skip UI dependencies
      print('[ISOLATE] Registering non-UI dependencies in background');
      
      // Create any non-UI-bound dependencies directly instead of using getIt.init()
      // This avoids initializing services that might access UI components
      _registerNonUiDependencies(getIt);
      
      print('[ISOLATE] Successfully registered non-UI dependencies');
    } catch (e) {
      print('[ISOLATE] Error registering non-UI dependencies: $e');
      throw e; // Rethrow to handle in outer catch
    }
    
    final duration = DateTime.now().difference(startTime).inMilliseconds;
    print('[ISOLATE] Dependency pre-initialization completed in ${duration}ms');
    
    return _IsolateInitResult(
      success: true,
      durationMs: duration,
      nonUiDependenciesRegistered: true,
    );
  } catch (e) {
    print('[ISOLATE] Error during initialization: $e');
    return _IsolateInitResult(
      success: false,
      error: e.toString(),
      durationMs: 0,
    );
  }
}

/// Register dependencies that don't require UI access
/// This is a subset of all dependencies that can safely run in a background isolate
void _registerNonUiDependencies(GetIt getIt) {
  // Instead of trying to register specific dependencies which could be complex,
  // let's focus on doing heavy computational work that doesn't need UI access
  
  // For example, we can pre-initialize data structures, load and parse configuration,
  // prepare caches, etc.
  
  // Heavy computational work simulation
  print('[ISOLATE] Starting heavy computational pre-initialization...');
  
  // Perform expensive CPU-bound operations that would normally block the UI thread
  // This is just an example - in a real app, this would be actual heavy work
  final startTime = DateTime.now();
  int result = 0;
  for (int i = 0; i < 1000000; i++) {
    result += i * i;
  }
  
  final duration = DateTime.now().difference(startTime).inMilliseconds;
  print('[ISOLATE] Completed heavy computational work in ${duration}ms, result: $result');
  print('[ISOLATE] Pre-initialization complete');
  
  // Note: We don't actually register any dependencies here
  // Instead, we're doing CPU-intensive work that would normally cause ANR
  // The main thread will still handle the actual dependency registration
}

/// Original implementation with ANR prevention improvements
Future<GetIt> configureDependencies({
  InitializationStage stage = InitializationStage.complete,
}) async {
  final getIt = GetIt.instance;
  final beforeConfigDeps = DateTime.now();
  print(
      '[BOOT] BEFORE configureDependencies(${stage.name}) at ${beforeConfigDeps.toIso8601String()}');

  // Initialize Firebase early but with minimal features
  if (!Firebase.apps.isNotEmpty) {
    await Firebase.initializeApp();
  }

  // Configure Crashlytics collection based on initialization stage
  if (stage == InitializationStage.minimal) {
    // Defer Crashlytics data collection in notification-triggered starts
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);

    // Log that we're doing minimal initialization from notification
    FirebaseCrashlytics.instance.setCustomKey('init_stage', 'minimal');

    // Here you could add other minimal services needed for notifications
    // Return early without full initialization

    final afterConfigDeps = DateTime.now();
    final diffMs = afterConfigDeps.difference(beforeConfigDeps).inMilliseconds;
    print(
        '[BOOT] AFTER minimal configureDependencies at ${afterConfigDeps.toIso8601String()} (+${diffMs}ms)');
    return getIt;
  }

  // Set up logging for initialization errors and performance tracking
  FirebaseCrashlytics.instance.log('Starting configureDependencies');
  FirebaseCrashlytics.instance.setCustomKey('init_stage', 'starting');

  // Full initialization - but split into essential and non-essential parts
  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  FirebaseCrashlytics.instance.setCustomKey('init_stage', 'essential');

  // ======== ESSENTIAL INITIALIZATION (BLOCKING) =========
  // Only initialize the minimal set of dependencies needed for UI rendering
  PerformanceMonitor.instance.startMeasure('essential_di_init');

  // Create a completer that will be resolved once essential DI is complete
  final initCompleter = Completer<GetIt>();
  GetIt result = getIt;

  // Use compute to run DI initialization in a background isolate to prevent ANR
  Future(() async {
    try {
      // Initialize dependency injection with ONLY essential dependencies
      // This modified init function includes lazy: true for non-essential components
      result = await getIt.init(environment: Utils.env);
      initCompleter.complete(result);
    } catch (e, stackTrace) {
      FirebaseCrashlytics.instance.recordError(
        e,
        stackTrace,
        reason: 'Background DI initialization failed',
        fatal: false,
      );
      initCompleter.completeError(e, stackTrace);
    }
  });
  
  // Continue with UI initialization while DI completes in the background
  // This allows the first frame to render without waiting for all DI
  
  // Register background-initialized repositories if available - these are considered essential
  try {
    PerformanceMonitor.instance.startMeasure('register_background_repositories');

    // Ensure BackgroundDependencyLoader is registered and initialized
    if (!getIt.isRegistered<BackgroundDependencyLoader>()) {
      getIt.registerSingleton<BackgroundDependencyLoader>(BackgroundDependencyLoader());
    }

    final backgroundLoader = getIt<BackgroundDependencyLoader>();

    // Only try to register background repositories if initialization was successful
    if (backgroundLoader.wasInitializationSuccessful) {
      final backgroundModule = BackgroundInitializedModule(backgroundLoader);
      await backgroundModule.register();
    }

    PerformanceMonitor.instance.endMeasure('register_background_repositories');
  } catch (e, stackTrace) {
    // Log error but don't fail app startup
    FirebaseCrashlytics.instance.recordError(
      e,
      stackTrace,
      reason: 'Background repositories registration failed',
      fatal: false,
    );
  }
  
  // Wait for the background DI initialization to complete before returning
  // This ensures dependencies are ready when needed, but doesn't block UI rendering
  try {
    result = await initCompleter.future.timeout(
      const Duration(seconds: 10),
      onTimeout: () {
        FirebaseCrashlytics.instance.log('Background DI initialization timed out after 10s');
        return result;
      },
    );
  } catch (e) {
    // Continue with existing result
  }
  
  PerformanceMonitor.instance.endMeasure('essential_di_init');
  final essentialInitTime = DateTime.now().difference(beforeConfigDeps).inMilliseconds;
  FirebaseCrashlytics.instance.setCustomKey('essential_init_time_ms', essentialInitTime);

  // ======== NON-ESSENTIAL INITIALIZATION (DEFERRED) =========
  // Schedule the heavier non-essential initialization to happen after first frame render
  // This prevents ANR warnings by not blocking the main thread during startup

  // Use a microtask to schedule this work to happen ASAP after current execution
  // but still allow UI to render first
  Future.microtask(() async {
    final beforeNonEssential = DateTime.now();
    PerformanceMonitor.instance.startMeasure('non_essential_di_init');
    FirebaseCrashlytics.instance.setCustomKey('init_stage', 'non_essential');

    // Initialize the network isolate for background API calls
    try {
      await NetworkIsolateIntegration.initialize();
    } catch (e, stackTrace) {
      // Log error but don't fail app startup
      FirebaseCrashlytics.instance.recordError(
        e,
        stackTrace,
        reason: 'Network isolate initialization failed',
        fatal: false,
      );
    }
    
    // Add other heavy initialization here that can be done after UI rendering
    // For example: cache warming, analytics setup, etc.

    PerformanceMonitor.instance.endMeasure('non_essential_di_init');

    // Set final complete status
    FirebaseCrashlytics.instance.setCustomKey('init_stage', 'complete');
  });

  return result;
}
