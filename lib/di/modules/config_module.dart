import 'dart:async';
import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/services.dart';
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import 'package:injectable/injectable.dart';
import 'package:x1440/firebase_options.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
import 'package:x1440/utils/Utils.dart';

@module
abstract class ModuleAppConfig {
  @preResolve
  @Singleton()
  Future<FirebaseRemoteConfig> firebaseRemoteConfig() async {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
    final remoteConfig = FirebaseRemoteConfig.instance;
    remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(minutes: 5),
    ));

    return remoteConfig;
  }

  @preResolve
  @Singleton()
  Future<AppConfig> appConfig(FirebaseRemoteConfig firebaseRemoteConfig) async {
    String stringJsonConfigs;
    const env = Utils.env;

    dynamic jsonConfigs;

    try {
      // Add timeout to prevent long startup delays
      await firebaseRemoteConfig.fetchAndActivate().timeout(
        const Duration(seconds: 3),
        onTimeout: () {
          throw TimeoutException('Firebase Remote Config timeout');
        },
      );
      stringJsonConfigs = firebaseRemoteConfig.getString('${env}_1440');
      jsonConfigs = jsonDecode(stringJsonConfigs);
    } catch (e) {
      stringJsonConfigs =
          await rootBundle.loadString('lib/config/${env}_1440.json');
      try {
        jsonConfigs = jsonDecode(stringJsonConfigs);
      } catch (e) {
        throw Exception('Error parsing local json configs');
      }
    }

    // set the only device orientation to be portrait
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    AppConfig appConfig = AppConfig.fromJson(jsonConfigs);
    return appConfig;
  }

  @preResolve
  @Singleton()
  Future<FlutterWebAuth2Options> flutterWebAuth2Options() async {
    final customTabsPackageOrder =  <String>[
      if (Utils.isIntegrationTest) 'com.airwatch.browser',
      'com.android.chrome',
      "com.chrome.beta",
      "com.chrome.dev",
      "com.microsoft.emmx"
    ];

    return FlutterWebAuth2Options(
        preferEphemeral: false, customTabsPackageOrder: customTabsPackageOrder);
  }
}
