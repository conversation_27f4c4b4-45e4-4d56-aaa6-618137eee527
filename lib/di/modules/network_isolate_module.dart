import 'package:get_it/get_it.dart';
import 'package:x1440/api/isolate/network_service_factory.dart';
import 'package:x1440/api/salesforce/salesforce_api.dart';
import 'package:x1440/api/shim_service_api.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
// Still need LocalStorageRepository for auth listener
import 'package:x1440/repositories/storage/local_storage_repository.dart';
// No longer need this import as the dependency is handled internally
// import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';

class NetworkIsolateModule {
  final GetIt _getIt;
  
  NetworkIsolateModule(this._getIt);
  
  Future<void> register() async {
    // With the new @factoryMethod approach, NetworkServiceFactory should be
    // registered and initialized by the injectable system
    
    // But if it's not registered yet (which might happen in some cases),
    // we'll create and register it manually
    if (!_getIt.isRegistered<NetworkServiceFactory>()) {
      print('Creating and registering NetworkServiceFactory');
      
      // With our new implementation, we don't need to pass dependencies to NetworkServiceFactory.create()
      // The factory will get them internally during initialization
      
      // Check if app was launched from notification
      bool isNotificationTriggeredStart = false;
      try {
        // We can't directly call checkIfLaunchedFromNotification here as it might be a duplicate call
        // This will be handled in main.dart and passed to NetworkServiceFactory.create()
        print('NetworkIsolateModule: Not checking notification launch status here');
      } catch (e) {
        print('Error checking notification launch status: $e');
      }
      
      // Create and initialize a new instance
      final factory = await NetworkServiceFactory.create(
        isNotificationTriggeredStart: isNotificationTriggeredStart,
      );
      
      // Register it in GetIt
      _getIt.registerSingleton<NetworkServiceFactory>(factory);
    }
    
    // Listen to auth changes to update tokens in the network isolate
    _setupAuthListeners();
    
    // Register the API clients created by the factory
    _registerApiClients();
  }
  
  /// Register API client factories that use the network isolate
  void _registerApiClients() {
    // Get the instance URLs for APIs from app config
    final appConfig = _getIt<AppConfig>();
    final shimServiceUrl = appConfig.shimServiceUrl;
    
    // For Salesforce URL, we need to use the appropriate environment config
    // Using productionSalesforceConfig as default if available, or fallback to sandbox
    final sfConfig = appConfig.productionSalesforceConfig ?? appConfig.sandboxSalesforceConfig;
    final salesforceUrl = sfConfig?.endPointBase ?? '';
    
    // Get the network service factory
    final networkServiceFactory = _getIt<NetworkServiceFactory>();
    
    // Skip registering ShimServiceApi if it's already registered by the DI system
    // This avoids conflicts with the shimServiceApiFactory in base_modules.dart
    if (!_getIt.isRegistered<ShimServiceApi>()) {
      _getIt.registerFactory<ShimServiceApi>(
        () => networkServiceFactory.getShimServiceApi(shimServiceUrl),
      );
    } else {
      print('ShimServiceApi already registered, skipping registration in NetworkIsolateModule');
    }
    
    // Register SalesforceApi from the network isolate factory
    // Override existing registration by unregistering first if needed
    if (_getIt.isRegistered<SalesforceApi>()) {
      _getIt.unregister<SalesforceApi>();
    }
    _getIt.registerFactory<SalesforceApi>(
      () => networkServiceFactory.getSalesforceApi(salesforceUrl),
    );
  }
  
  void _setupAuthListeners() {
    // Safely get AuthBloc if available
    final networkServiceFactory = _getIt<NetworkServiceFactory>();

    // Check if AuthBloc is registered before trying to use it
    if (_getIt.isRegistered<AuthBloc>()) {
      final authBloc = _getIt<AuthBloc>();

      // Add a subscription to handle auth state changes
      authBloc.stream.listen((state) async {
        // On login or token refresh, update the tokens in the network isolate
        if (state.isLoggedIn) {
          final credentials = await _getIt<LocalStorageRepository>().getCredentials();

          // Update tokens in the network isolate
          await networkServiceFactory.updateAuthTokens(
            // For Shim API
            authorizationToken: credentials.authorizationToken,
            // For Salesforce API
            accessToken: credentials.sessionToken,
          );
        }
      });
    }

    // CRITICAL: Also listen to credentials stream changes to update network isolate
    // This ensures that when credentials are updated (e.g., token refresh), the network isolate gets the fresh tokens
    if (_getIt.isRegistered<LocalStorageRepository>()) {
      final localStorage = _getIt<LocalStorageRepository>();

      localStorage.credentialsStream.listen((credentials) async {
        try {
          print('🔄 NETWORK_ISOLATE_MODULE - Credentials stream changed, updating network isolate');
          print('🔄 NETWORK_ISOLATE_MODULE - Authorization token present: ${credentials.authorizationToken != null}');
          print('🔄 NETWORK_ISOLATE_MODULE - Session token present: ${credentials.sessionToken != null}');

          // Update tokens in the network isolate with fresh credentials
          await networkServiceFactory.updateAuthTokens(
            // For Shim API
            authorizationToken: credentials.authorizationToken,
            // For Salesforce API
            accessToken: credentials.sessionToken,
          );

          print('✅ NETWORK_ISOLATE_MODULE - Successfully updated network isolate with fresh credentials');
        } catch (error) {
          print('❌ NETWORK_ISOLATE_MODULE - Error updating network isolate with credentials: $error');
        }
      });
    }
  }
  
  // Method to be called when the app is shutting down
  void dispose() {
    _getIt<NetworkServiceFactory>().dispose();
  }
}
