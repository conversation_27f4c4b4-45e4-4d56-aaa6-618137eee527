import 'package:get_it/get_it.dart';
import 'package:x1440/api/isolate/network_service_factory.dart';
import 'package:x1440/api/salesforce/salesforce_api.dart';
import 'package:x1440/api/shim_service_api.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';

class NetworkIsolateModule {
  final GetIt _getIt;
  
  NetworkIsolateModule(this._getIt);
  
  Future<void> register() async {
    // Register the network service factory as a singleton
    // Use the factory method instead of constructor
    if (!_getIt.isRegistered<NetworkServiceFactory>()) {
      // Create and initialize a new instance asynchronously
      final factory = await NetworkServiceFactory.create();
      _getIt.registerSingleton<NetworkServiceFactory>(factory);
    }
    
    // Initialize the network service factory
    // Dependencies may not be available yet during initialization
    // Try to get them, but don't fail if they're not registered yet
    LoggingUseCase? loggingUseCase;
    LocalStorageRepository? localStorageRepository;
    
    try {
      if (_getIt.isRegistered<LoggingUseCase>()) {
        loggingUseCase = _getIt<LoggingUseCase>();
      }
    } catch (e) {
      print('LoggingUseCase not yet available for network isolate: $e');
    }
    
    try {
      if (_getIt.isRegistered<LocalStorageRepository>()) {
        localStorageRepository = _getIt<LocalStorageRepository>();
      }
    } catch (e) {
      print('LocalStorageRepository not yet available for network isolate: $e');
    }
    
    await _getIt<NetworkServiceFactory>().initialize(
      loggingUseCase: loggingUseCase,
      localStorageRepository: localStorageRepository,
    );
    
    // Listen to auth changes to update tokens in the network isolate
    _setupAuthListeners();
    
    // Register the API clients created by the factory to override
    // the standard Dio-based implementations
    _registerApiClients();
  }
  
  /// Register API client factories that use the network isolate
  void _registerApiClients() {
    // Get the instance URLs for APIs from app config
    final appConfig = _getIt<AppConfig>();
    final shimServiceUrl = appConfig.shimServiceUrl;
    
    // For Salesforce URL, we need to use the appropriate environment config
    // Using productionSalesforceConfig as default if available, or fallback to sandbox
    final sfConfig = appConfig.productionSalesforceConfig ?? appConfig.sandboxSalesforceConfig;
    final salesforceUrl = sfConfig?.endPointBase ?? '';
    
    // Get the network service factory
    final networkServiceFactory = _getIt<NetworkServiceFactory>();
    
    // Register ShimServiceApi from the network isolate factory
    // Override existing registration by unregistering first if needed
    if (_getIt.isRegistered<ShimServiceApi>()) {
      _getIt.unregister<ShimServiceApi>();
    }
    _getIt.registerFactory<ShimServiceApi>(
      () => networkServiceFactory.getShimServiceApi(shimServiceUrl),
    );
    
    // Register SalesforceApi from the network isolate factory
    // Override existing registration by unregistering first if needed
    if (_getIt.isRegistered<SalesforceApi>()) {
      _getIt.unregister<SalesforceApi>();
    }
    _getIt.registerFactory<SalesforceApi>(
      () => networkServiceFactory.getSalesforceApi(salesforceUrl),
    );
  }
  
  void _setupAuthListeners() {
    // Safely get AuthBloc if available
    final networkServiceFactory = _getIt<NetworkServiceFactory>();

    // Check if AuthBloc is registered before trying to use it
    if (_getIt.isRegistered<AuthBloc>()) {
      final authBloc = _getIt<AuthBloc>();

      // Add a subscription to handle auth state changes
      authBloc.stream.listen((state) async {
        // On login or token refresh, update the tokens in the network isolate
        if (state.isLoggedIn) {
          final credentials = await _getIt<LocalStorageRepository>().getCredentials();

          // Update tokens in the network isolate
          await networkServiceFactory.updateAuthTokens(
            // For Shim API
            authorizationToken: credentials.authorizationToken,
            // For Salesforce API
            accessToken: credentials.sessionToken,
          );
        }
      });
    }

    // CRITICAL: Also listen to credentials stream changes to update network isolate
    // This ensures that when credentials are updated (e.g., token refresh), the network isolate gets the fresh tokens
    if (_getIt.isRegistered<LocalStorageRepository>()) {
      final localStorage = _getIt<LocalStorageRepository>();

      localStorage.credentialsStream.listen((credentials) async {
        try {
          print('🔄 NETWORK_ISOLATE_MODULE - Credentials stream changed, updating network isolate');
          print('🔄 NETWORK_ISOLATE_MODULE - Authorization token present: ${credentials.authorizationToken != null}');
          print('🔄 NETWORK_ISOLATE_MODULE - Session token present: ${credentials.sessionToken != null}');

          // Update tokens in the network isolate with fresh credentials
          await networkServiceFactory.updateAuthTokens(
            // For Shim API
            authorizationToken: credentials.authorizationToken,
            // For Salesforce API
            accessToken: credentials.sessionToken,
          );

          print('✅ NETWORK_ISOLATE_MODULE - Successfully updated network isolate with fresh credentials');
        } catch (error) {
          print('❌ NETWORK_ISOLATE_MODULE - Error updating network isolate with credentials: $error');
        }
      });
    }
  }
  
  // Method to be called when the app is shutting down
  void dispose() {
    _getIt<NetworkServiceFactory>().dispose();
  }
}
