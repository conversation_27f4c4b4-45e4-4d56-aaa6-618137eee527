import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/frameworks/message_queue/message_queue_manager.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/utils/loggy_utils.dart';

import 'notifications/notifications_event.dart';
import 'notifications/notifications_manager.dart';

/// NOTE: this is part of the Olivier-joins-refactoring; not an original Service
class MessagingService with ServiceLoggy {
  final MessageQueueManager _messageQueueManager;
  final MessagingUseCase _messagingUseCase;
  final ConversationsUseCase _conversationsUseCase;
  final AuthUseCase _authUseCase;
  final RemoteLogger _logger;

  MessagingService(this._messageQueueManager, this._messagingUseCase,
      this._conversationsUseCase, this._authUseCase, this._logger);

  final List<String> _handledQueueReceivedMessages = [];
  List<int> get handledQueueReceivedMessagesInts => _handledQueueReceivedMessages.map((e) => int.parse(e)).toList();

  Future<void> init() async {
    _messageQueueManager.messageSendQueue
        .listen(_messagingUseCase.onMessageSendQueue);

    _messageQueueManager.messageReceiveQueue.listen((data) {
      if (data.isEmpty) {
        return;
      }
      for (final message in data) {
        var alreadyHandled =
        handledQueueReceivedMessagesInts.contains(int.parse(message.notificationId));
        _logger.info(
            'onMessageReceivedFromQueue: ${message.notificationId}; action: ${message.notificationAction}; alreadyHandled: $alreadyHandled; type: ${message.shimServicePayload['messageType']}; payload: ${message.shimServicePayload}');

        if (alreadyHandled) {
          _logger.info(
              'onMessageReceivedFromQueue: already handled; sending ack for ${message.notificationId}');
          GetIt.I<NotificationManager>().add(AcknowledgeNotificationsEvent([message.notificationId]));
          // _messagingUseCase.acknowledgeNotifications([message.notificationId]);
          // _messageQueueManager.removeMessageIdsFromReceiveQueue(
          //     [message.notificationId]);
          return;
        }
        _handledQueueReceivedMessages.add(message.notificationId);
      }
      _conversationsUseCase.onMessageQueueReceive(data);
      _messagingUseCase.onMessageReceiveQueue(data);
    });

    _authUseCase.credentialsStream.listen((Credentials credentials) {
      // HYPOTHESIS 4: Add comprehensive logging for credentials stream
      loggy.info('🔍 CREDENTIALS_STREAM_DEBUG: Received credentials update');
      loggy.info('🔍 CREDENTIALS_STREAM_DEBUG: webSocketUrl present: ${credentials.webSocketUrl != null}');
      loggy.info('🔍 CREDENTIALS_STREAM_DEBUG: webSocketUrl: "${credentials.webSocketUrl}"');
      loggy.info('🔍 CREDENTIALS_STREAM_DEBUG: sessionToken present: ${credentials.sessionToken != null}');
      loggy.info('🔍 CREDENTIALS_STREAM_DEBUG: authorizationToken present: ${credentials.authorizationToken != null}');

      if (credentials.webSocketUrl != null) {
        loggy.info('🚀 CREDENTIALS_STREAM_DEBUG: Triggering websocket connection...');
        _messagingUseCase.connectToWebsocket();
        loggy.info('✅ CREDENTIALS_STREAM_DEBUG: connectToWebsocket() call completed');
      } else {
        loggy.info('🔌 CREDENTIALS_STREAM_DEBUG: Disconnecting websocket (no webSocketUrl)');
        _messagingUseCase.disconnectWebsocket();
      }
    });
  }

  @visibleForTesting // TODO: change test?
  Future<void> addQueueSendMessage(QueueSendMessage message) =>
      _messageQueueManager.addMessageToSendQueue(message);
}
