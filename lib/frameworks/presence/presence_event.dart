import 'package:x1440/api/dtos/presence_status.dart';

abstract class PresenceEvent {}
class UpdatePresenceStatusEvent extends PresenceEvent {
  final PresenceStatus? presenceStatus;
  UpdatePresenceStatusEvent([this.presenceStatus]);
}
class SetAvailablePresenceStatusesEvent extends PresenceEvent {
  final List<PresenceStatus> availableStatuses;
  final String? activeStatusId;
  SetAvailablePresenceStatusesEvent(this.availableStatuses, [this.activeStatusId]);
}
class SetUserOfflineEvent extends PresenceEvent {}

class InitializePresenceForExistingSessionEvent extends PresenceEvent {}