import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/presence/presence_use_case.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';

import 'presence_event.dart';
import 'presence_state.dart';

// TODO: refactor -- these belong in the PresenceManager, most of PresenceManager belongs in a UseCase/Repo
class PresenceManager extends Bloc<PresenceEvent, PresenceState> {
  PresenceManager(super.initialState);
}

class PresenceManagerImpl extends PresenceManager {
  final PresenceUseCase _presenceUseCase;
  final SessionUseCase _sessionUseCase;
  final MessagingUseCase _messagingUseCase;

  PresenceManagerImpl(this._presenceUseCase, this._sessionUseCase, this._messagingUseCase) : super(PresenceState()) {
    on<UpdatePresenceStatusEvent>(_onUpdatePresenceStatusEvent);
    on<SetAvailablePresenceStatusesEvent>(_onSetAvailablePresenceStatusesEvent);
    on<SetUserOfflineEvent>(_onSetUserOfflineEvent);
    on<InitializePresenceForExistingSessionEvent>(_onInitializePresenceForExistingSessionEvent);
  }

  /// Initialize presence status for existing sessions
  /// This is called when the user is already logged in and we need to set up the presence UI
  Future<void> _onInitializePresenceForExistingSessionEvent(
      InitializePresenceForExistingSessionEvent event, Emitter<PresenceState> emit) async {
    await _sessionUseCase.initializePresenceForExistingSession();
  }

  Future<void> _onSetUserOfflineEvent(SetUserOfflineEvent event, Emitter<PresenceState> emit) async {
    emit(state.copyWith(currentStatus: PresenceState().currentStatus));
  }

  Future<void> _onUpdatePresenceStatusEvent(UpdatePresenceStatusEvent event, Emitter<PresenceState> emit) async {
    final remoteLogger = GetIt.I<LoggingUseCase>().getRemoteLogger('PresenceManagerImpl');
    if (state.statusIsUpdating) {
      remoteLogger.warn('PresenceManagerImpl - status is already updating');
      return;
    }
    emit(state.copyWith(statusIsUpdating: true));
    /// if we're not currently online
    if (state.currentStatus.isOnline != true) {
      /// and the new status is online, start a session
      if (event.presenceStatus?.isOnline == true) {
        remoteLogger.info('PresenceManagerImpl - starting session');
        final sessionStarted = await _sessionUseCase.startSession();
        if (sessionStarted) {
          await _presenceUseCase.setActivePresenceStatus(event.presenceStatus!);
          _messagingUseCase.connectToWebsocket();
        } else {
          // TODO: handle this error case
          GetIt.I<AuthBloc>().add(LogoutEvent());
        }
      }
      return emit(state.copyWith(statusIsUpdating: false, currentStatus: event.presenceStatus ?? PresenceState().currentStatus));
    }
    /// if null (or other offline indication), end session
    if (event.presenceStatus?.isOnline != true) {
      _presenceUseCase.setPreviousLoggedInUserPresenceStatus(null);
      await GetIt.I<ConversationsService>().handlePresenceOffline();
    } else {
      await _presenceUseCase.setActivePresenceStatus(event.presenceStatus!);
    }
    emit(state.copyWith(statusIsUpdating: false, currentStatus: event.presenceStatus ?? PresenceState().currentStatus));
  }

  Future<void> _onSetAvailablePresenceStatusesEvent(SetAvailablePresenceStatusesEvent event, Emitter<PresenceState> emit) async {
    emit(state.copyWith(availableStatuses: event.availableStatuses));

    if (state.currentStatus.isOnline == true) {
      return;
    }

    String? activePresenceStatusId = event.activeStatusId ?? (await _presenceUseCase.getPreviousSessionActiveStatusId());

    final activeStatus = activePresenceStatusId == null ? null : state.getPresenceStatus(activePresenceStatusId);

    if (activeStatus?.id.isNotEmpty == true) {
      await _presenceUseCase.setActivePresenceStatus(activeStatus!);
      emit(state.copyWith(currentStatus: activeStatus));
    } else if (!state.statusIsUpdating) {
      emit(state.copyWith(currentStatus: PresenceState().currentStatus, showPresenceModal: UiEvent(Nothing())));
    }
  }
}