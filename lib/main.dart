import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_driver/driver_extension.dart';
import 'package:get_it/get_it.dart';
import 'package:loggy/loggy.dart';
import 'package:dio/dio.dart';
import 'package:x1440/api/isolate/network_service_factory.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/salesforce_token_body.dart';
import 'package:x1440/api/dtos/sessions_body.dart';
import 'package:x1440/api/dtos/refresh_salesforce_token_body.dart';
import 'package:x1440/repositories/auth/auth_repository.dart';
import 'package:x1440/repositories/session/session_repository_impl.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository_impl.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/use_cases/settings/settings_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/viewmodels/appwide_viewmodels.dart';
import 'di/di.dart';

import 'generated/l10n.dart';

/// Method channel for communicating with native code about app launch
const String appLaunchChannel = 'io.x1440/app_launch';

/// Check if the app was launched from a notification tap with retry mechanism
Future<bool> checkIfLaunchedFromNotification() async {
  // Create method channel with same name defined in MainActivity.kt
  const platform = MethodChannel(appLaunchChannel);
  
  // Maximum number of retry attempts
  const maxRetries = 5;
  // Starting delay (will increase with exponential backoff)
  int delayMs = 500;
  
  // Try up to maxRetries times with exponential backoff
  for (int attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Wait with increasing delay between attempts
      await Future.delayed(Duration(milliseconds: delayMs));

      // Attempt to invoke the method
      final bool result = await platform.invokeMethod('wasLaunchedFromNotification') ?? false;

      return result;
    } catch (e) {
      // If this was the last attempt, return false
      if (attempt >= maxRetries - 1) {
        return false;
      }

      // Exponential backoff - double the delay for next attempt
      delayMs *= 2;
    }
  }
  
  // This should never be reached due to the return in the loop, but just in case
  return false;
}

void main() async {
  print('main.dart: main() starting app launch');
  if (Utils.isIntegrationTest) {
    enableFlutterDriverExtension();
  }

  // Initialize Flutter binding with rendering policy that can help prevent ANRs
  final binding = WidgetsFlutterBinding.ensureInitialized();

  // Only briefly defer frame to help with initialization
  // but make sure to allow it to proceed afterward
  binding.deferFirstFrame();

  // Schedule allowing the frame to proceed after a short delay
  Future.delayed(const Duration(milliseconds: 300), () {
    binding.allowFirstFrame();
  });

  // Give the Flutter engine a moment to initialize // TODO: Is this necessary?
  await Future.delayed(const Duration(seconds: 1));

  /// IMPORTANT: this is required to support legacy view models that use S.current we've added in; TODO: remove this once all view models are removed => clean logic has localization strings handled on the UI layer!!!
  await S.load(Locale.fromSubtags(languageCode: Utils.getLanguage()));

  // We need NetworkServiceFactory to be registered before DI runs for ShimServiceApi,
  // but we need to ensure it's initialized in a way that doesn't cause AnimationController lifecycle issues
  try {
    // Check if app was launched from notification
    final wasLaunchedFromNotification = await checkIfLaunchedFromNotification();

    // Create and register NetworkServiceFactory before DI runs
    final factory = await NetworkServiceFactory.create(
      isNotificationTriggeredStart: wasLaunchedFromNotification,
    );
    GetIt.instance.registerSingleton<NetworkServiceFactory>(factory);
  } catch (e, stackTrace) {
    print('CRITICAL ERROR initializing NetworkServiceFactory: $e');
    // Don't rethrow - let the app continue but log the issue
  }

  // Now run the rest of the DI setup
  await configureDependencies();


  
  // Add a longer delay to make sure the app is fully initialized
  Future.delayed(Duration(seconds: 5), () async {
    print('🧪 TESTING - Delay complete, starting test');
    
    try {
      // Check if NetworkServiceFactory is registered in GetIt
      print('🧪 TESTING - Starting direct ShimServiceApi test with Network Isolate');
      
      print('🧪 TESTING - Checking if NetworkServiceFactory is available in DI');
      if (!GetIt.instance.isRegistered<NetworkServiceFactory>()) {
        print('🧪 TESTING - ERROR: NetworkServiceFactory is NOT registered in GetIt');
        return;
      }
      
      print('🧪 TESTING - NetworkServiceFactory is available in DI');
      
      // Get NetworkServiceFactory from DI
      final networkServiceFactory = GetIt.I<NetworkServiceFactory>();
      print('🧪 TESTING - Successfully obtained NetworkServiceFactory instance');
      
      // Get ShimServiceApi through the factory
      print('🧪 TESTING - Getting ShimServiceApi through NetworkServiceFactory');
      
      // Use a stopwatch to measure the performance
      final stopwatch = Stopwatch()..start();
      
      final shimServiceApi = networkServiceFactory.getShimServiceApi('https://shim-service-dev.1440.io');
      print('🧪 TESTING - Successfully obtained ShimServiceApi instance in ${stopwatch.elapsedMilliseconds}ms');
      print('🧪 TESTING - ShimServiceApi base URL: https://shim-service-dev.1440.io');
      
      // Test direct session API call (simpler than refresh token)
      print('🧪 TESTING - Starting simple session test');
      if (false) try {
        stopwatch.reset();
        print('🧪 TESTING - Calling startSession endpoint');
        
        final startSessionResult = await shimServiceApi.startSession(
          'test-org-id',  // orgId path parameter
          SessionsBody(
            userId: 'test-user-id',
            instanceUrl: 'https://test.salesforce.com',
            deviceToken: 'test-device-token',
            accessToken: 'test-access-token',
            channelPlatformTypes: ['Messaging'],
            locale: 'en_US',
          ), 
        ).timeout(Duration(seconds: 30));
        
        print('✅ TESTING - Session start SUCCESS in ${stopwatch.elapsedMilliseconds}ms: $startSessionResult');
      } catch (sessionError) {
        print('❌ TESTING - Session start ERROR in ${stopwatch.elapsedMilliseconds}ms: $sessionError');
        print('❌ DIAGNOSTIC - Error type: ${sessionError.runtimeType}');
        
        // More detailed error analysis
        if (sessionError is DioException) {
          print('❌ DIAGNOSTIC - Dio error: ${sessionError.type}');
          print('❌ DIAGNOSTIC - Response: ${sessionError.response}');
          if (sessionError.response != null) {
            print('❌ DIAGNOSTIC - Status code: ${sessionError.response!.statusCode}');
            print('❌ DIAGNOSTIC - Response data: ${sessionError.response!.data}');
          }
        }
        
        // Check for 401 Unauthorized errors specifically
        if (sessionError.toString().contains('401')) {
          print('❗ ANALYSIS - 401 Unauthorized error detected');
          print('❗ DIAGNOSTIC - This suggests either:');
          print('  1. The token format is incorrect');
          print('  2. The token is invalid or expired');
          print('  3. The token is not allowed for this operation');
          print('  4. The authorization headers may be incorrect');
          print('  5. The request format may not match backend expectations');
        }
      }
      
      print('🧪 TESTING - Direct session test completed');
      
      // Also try the original test with refresh token
      print('🧪 TESTING - Also testing refreshSalesforceToken endpoint');
      if (false) try {
        stopwatch.reset();
        await shimServiceApi.refreshSalesforceToken(
          'test-signature',
          DateTime.now().toIso8601String(),
          RefreshSalesforceTokenBody(
            refreshToken: 'test-refresh-token',
            instanceUrl: 'https://test.instance.url',
            orgId: 'test-org-id',
          ),
        ).timeout(Duration(seconds: 30));
        print('✅ TESTING - Call to refreshSalesforceToken completed successfully in ${stopwatch.elapsedMilliseconds}ms');
      } catch (requestError) {
        // We expect this to fail with auth errors, we just want to see the logs
        print('🧪 TESTING - refreshSalesforceToken failed as expected: $requestError');
      }
      
      // Test session start directly with the SessionRepository to avoid token refresh flows
      if (false) try {
        print('🧪 TESTING - Testing direct session start request to isolate 401 error');
        
        // Get NetworkServiceFactory from DI
        final networkFactory = GetIt.I<NetworkServiceFactory>();
        final String shimServiceBaseUrl = 'https://shim-service-dev.1440.io';
        print('🧪 TESTING - Using shim service base URL: $shimServiceBaseUrl');
        
        final shimServiceApi = networkFactory.getShimServiceApi(shimServiceBaseUrl);
        print('🧪 TESTING - Successfully obtained ShimServiceApi instance');
        
        // Create SessionRepository with the API client
        final sessionRepository = SessionRepositoryImpl(shimServiceApi);
        print('🧪 TESTING - Created SessionRepository instance');
        
        // Create mock session body with realistic-looking values
        final String mockOrgId = '00DHn0000019Y8bMAE'; // Use a realistic format
        final String mockUserId = '005Hn000000duXXIAY';
        final String mockInstanceUrl = 'https://1440co-dev-ed.my.salesforce.com';
        
        // Use a realistic Salesforce token format for the initial authentication
        final String mockSalesforceToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzYWxlc2ZvcmNlLXVzZXIiLCJleHAiOjk5OTk5OTk5OTl9.signature';
        
        print('🧪 TESTING - Starting CORRECT authentication flow');
        print('🧪 TESTING - STEP 1: Obtain Shim Service token using Salesforce token');
        
        // 1. First step: Call /shim-service/auth/salesforce/obtain-token with Salesforce token
        final salesforceTokenBody = SalesforceTokenBody(
          accessToken: mockSalesforceToken,
          orgId: mockOrgId,
          expirationSeconds: 3600
        );
        
        print('🧪 TESTING - Calling authenticateSalesforce with:');
        print('🧪 TESTING - orgId: $mockOrgId');
        print('🧪 TESTING - Salesforce accessToken: ${mockSalesforceToken.substring(0, 15)}...');
        
        // This call needs the Salesforce accessToken in the body to get the Shim authorizationToken
        final authStopwatch = Stopwatch()..start();
        try {
          final authRepository = GetIt.I<AuthRepository>();
          final authResult = await authRepository.authShimServiceFromSalesforce(salesforceTokenBody);
          
          print('✅ TESTING - Authentication SUCCESS in ${authStopwatch.elapsedMilliseconds}ms');
          
          // Extract the shim service authorization token from the response
          String? shimAuthorizationToken;
          if (authResult is Success) {
            final response = (authResult as Success).data;
            print('✅ TESTING - Got Shim authorization token: ${response.accessToken?.substring(0, 15) ?? 'NULL'}...');
            shimAuthorizationToken = response.accessToken;
          } else if (authResult is Error) {
            final error = (authResult as Error).error;
            print('❌ TESTING - Error getting Shim token: ${error.message}');
            throw error;
          }
          
          if (shimAuthorizationToken == null) {
            print('❌ TESTING - Got null Shim authorization token');
            throw Exception('Null authorization token received');
          }
          
          // 2. Update the network isolate with the real Shim authorization token
          print('🧪 TESTING - STEP 2: Updating network isolate with real Shim token');
          await networkFactory.updateAuthTokens(authorizationToken: shimAuthorizationToken);
          print('✅ TESTING - Network isolate updated with real Shim token');
          
          // 3. Now create the sessions body with the user ID and other info
          print('🧪 TESTING - STEP 3: Creating session request with user data');
          final sessionsBody = SessionsBody(
            accessToken: shimAuthorizationToken, // Use the obtained Shim token here
            userId: mockUserId,
            instanceUrl: mockInstanceUrl,
            locale: 'en_US',
            expirationSeconds: 3600,
            deviceToken: 'test-device-token-${DateTime.now().millisecondsSinceEpoch}',
            deviceType: 'flutter',
            channelPlatformTypes: ['omni', 'x1440'],
            metadata: {}
          );
          
          // 4. Make the actual sessions request with the correct token
          print('🧪 TESTING - STEP 4: Making session start request with correct token');
          final endpoint = '/shim-service/organizations/$mockOrgId/sessions';
          print('🧪 TESTING - Endpoint: $endpoint');
          print('🧪 TESTING - Full URL: $shimServiceBaseUrl$endpoint');
          
          final sessionStopwatch = Stopwatch()..start();
          final result = await sessionRepository.startSession(mockOrgId, sessionsBody);
          
          // 5. Detailed result logging
          print('✅ TESTING - Session start SUCCESS in ${sessionStopwatch.elapsedMilliseconds}ms: $result');
        } catch (authError) {
          print('❌ TESTING - Authentication ERROR in ${authStopwatch.elapsedMilliseconds}ms: $authError');
          
          if (authError is DioException && authError.response != null) {
            print('❌ DIAGNOSTIC - Status code: ${authError.response!.statusCode}');
            print('❌ DIAGNOSTIC - Response data: ${authError.response!.data}');
              
            if (authError.response!.statusCode == 401) {
              print('❗ ANALYSIS - 401 Unauthorized in authentication step');
              print('❗ DIAGNOSTIC - This suggests:');
              print('  1. The Salesforce accessToken is invalid or expired');
              print('  2. The orgId format is incorrect');
              print('  3. The request format may not match backend expectations');
            }
          } else if (authError is ApiError) {
            print('❌ DIAGNOSTIC - ApiError: ${authError.message}');
          }
          
          rethrow;
        }
      } catch (error) {
        print('❌ TESTING - Overall flow ERROR: $error');
          
        if (error is DioException && error.response != null) {
          print('❌ DIAGNOSTIC - Status code: ${error.response!.statusCode}');
          print('❌ DIAGNOSTIC - Response data: ${error.response!.data}');
            
          if (error.response!.statusCode == 401) {
            print('❗ ANALYSIS - 401 Unauthorized error detected');
            print('❗ DIAGNOSTIC - This suggests either:');
            print('  1. The token format is incorrect');
            print('  2. The token is invalid or expired');
            print('  3. The token is not allowed for this operation');
            print('  4. The authorization headers may be incorrect');
            print('  5. The request format may not match backend expectations');
          }
        } else if (error is ApiError) {
          print('❌ DIAGNOSTIC - ApiError: ${error.message}');
        }
        
        print('🧪 TESTING - Direct session test completed with errors');
      }

      // Alternative test: Direct Dio request to bypass NetworkIsolate
    } catch (e) {  
      print('🧪 TESTING - Error in test code: $e');
    }
    
    print('🧪 TESTING - Test function complete');

  // Add WebSocket debugging test
  print('🧪 WEBSOCKET_DEBUG - Starting WebSocket connection test');
  try {
    final shimWebsocket = GetIt.I<ShimWebsocketRepository>();
    if (shimWebsocket is ShimWebsocketRepositoryImpl) {
      await (shimWebsocket as ShimWebsocketRepositoryImpl).debugTestConnection();
    }
  } catch (e) {
    print('🧪 WEBSOCKET_DEBUG - Error during WebSocket test: $e');
  }
  print('🧪 WEBSOCKET_DEBUG - WebSocket test complete');
  });

  /// this must run after configureDependencies; it will set scopes if necessary ('demo') which can potentially require any/all dependencies to have been registered.
  GetIt.I<SettingsUseCase>().init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget with UiLoggy {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) => getAppWideViewModels();
}
