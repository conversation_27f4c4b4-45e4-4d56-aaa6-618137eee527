import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/salesforce/dtos/quick_actions/quick_action.dart';
import 'package:x1440/api/salesforce/dtos/salesforce_soql_query_response.dart';
import 'package:x1440/api/salesforce/salesforce_api.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';

class QueryRepository {
  RemoteLogger get _logger => GetIt.I<LoggingUseCase>().getRemoteLogger('QueryRepository');
  final LocalStorageRepository _localStorageRepository;

  QueryRepository(this._localStorageRepository);

  Future<Result<SalesforceSoqlQueryResponse, ApiError>>
      queryMEUByMessagingSessionId(SfId messagingSessionId) => _query(
          "SELECT MessagingEndUserId FROM MessagingSession WHERE Id='$messagingSessionId'");

  // TODO: Implement -- this is the conversationEntries query from SalesforceApiService
  /*
    services/data/v58.0/query?q=SELECT+ActorId,ActorName,ActorType,ConversationId,CreatedDate,EntryType,EntryTime,Id,Message,Seq,HasAttachments+FROM+ConversationEntry+WHERE+ConversationId+IN+
     */
  // Future<Result<SalesforceSoqlQueryResponse, ApiError>> queryConversationEntries(
  //     String conversationIdsString) =>
  //     _query(
  //         "SELECT ActorId, ActorName, ActorType, ConversationId, CreatedDate, EntryType, EntryTime, Id, Message, Seq, HasAttachments FROM ConversationEntry WHERE ConversationId IN ($conversationIdsString)");

  Future<
      Result<SalesforceSoqlQueryResponse,
          ApiError>> queryMessagingChannels() => _query(
      "SELECT Id, MessageType, DeveloperName, MasterLabel, MessagingPlatformKey, PlatformType, Language, IsoCountryCode, IsActive, isDeleted FROM MessagingChannel");

  Future<
      Result<SalesforceSoqlQueryResponse,
          ApiError>> queryQuickActionsList(QuickActionsType actionsType) => _query(
      "SELECT FIELDS(CUSTOM) FROM ${actionsType.value}_quick_actions_list__c ORDER BY action_order__c LIMIT 200");

  Future<Result<SalesforceSoqlQueryResponse, ApiError>> queryAgentWork({
    required String userId,
    required String idsString,
  }) {
    final query = "SELECT Id, Status, LastModifiedDate, WorkItemId, OwnerId, UserId FROM AgentWork WHERE UserId='$userId' AND workItemId IN ($idsString) ORDER BY WorkItemId, LastModifiedDate DESC";
    _logger.info('QueryRepository - queryAgentWork: executing query for userId=$userId, idsString=$idsString');
    _logger.info('QueryRepository - queryAgentWork: SOQL query: $query');

    /// NOTE: when Enhanced Omni is turned on, 'OwnerId' is (or can be) the automated process, even when the work is opened and assigned to a user
    return _query(query);
  }

  Future<Result<SalesforceSoqlQueryResponse, ApiError>> conversationEntries(
      List<String> conversationIds) async {
    if (conversationIds.isEmpty) {
      return Error(ApiError.createError(Exception('conversationIds is empty')));
    }
    final query =
        "SELECT Id, ConversationIdentifier, ConversationChannelId FROM Conversation WHERE Id IN (${conversationIds.map((id) => "'$id'").join(',')}) AND ConversationIdentifier != null";

    final result = await _query(query);

    return result;
  }

  Future<Result<SalesforceSoqlQueryResponse, ApiError>> queryMessagingEndUsers({
    SfId? contactId,
    SfId? messagingChannelId,
    String? messagingPlatformKey,
    SfId? id,
  }) async {
    String queryString =
        "SELECT ContactId, Id, IsDeleted, MessagingChannelId, Name, ProfilePictureUrl, MessagingPlatformKey FROM MessagingEndUser";

    bool hasWhered = false;
    if (contactId != null) {
      hasWhered = true;
      queryString += " WHERE ContactId = '$contactId'";
    }
    if (messagingChannelId != null) {
      if (hasWhered) {
        queryString += " AND MessagingChannelId = '$messagingChannelId'";
      } else {
        queryString += " WHERE MessagingChannelId = '$messagingChannelId'";
        hasWhered = true;
      }
    }
    if (messagingPlatformKey != null) {
      if (hasWhered) {
        queryString += " AND MessagingPlatformKey = '$messagingPlatformKey'";
      } else {
        queryString += " WHERE MessagingPlatformKey = '$messagingPlatformKey'";
        hasWhered = true;
      }
    }
    if (id != null) {
      if (hasWhered) {
        queryString += " AND Id = '$id'";
      } else {
        queryString += " WHERE Id = '$id'";
        hasWhered = true;
      }
    }

    return _query(queryString);
  }

  Future<Result<SalesforceSoqlQueryResponse, ApiError>> _query(
      String query) async {
    try {
      _logger.info('QueryRepository - _query: starting query execution');
      final credentials = await _localStorageRepository.getCredentials();
      if (credentials.instanceUrl != null) {
        _logger.info('QueryRepository - _query: creating SalesforceApi with baseUrl: ${credentials.instanceUrl}');
        final sfApi = SalesforceApi(GetIt.instance<Dio>(),
            baseUrl: credentials.instanceUrl!);
        _logger.info('QueryRepository - _query: calling soqlQuerySalesForceObject');
        final response = await sfApi.soqlQuerySalesForceObject(query);
        _logger.info('QueryRepository - _query: query successful, received ${response.records.length} records');
        return Success(response);
      } else {
        _logger.error('QueryRepository - _query: No credentials found for query: $query');
        return Error(ApiError.createError(
            Exception('_query - No credentials found: $query')));
      }
    } catch (error) {
      _logger.error('QueryRepository - _query: query failed with error: $error');
      return Error(ApiError.createError(error as Exception));
    }
  }
}
