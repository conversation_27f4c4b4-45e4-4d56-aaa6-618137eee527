import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:sembast/sembast.dart';
import 'package:x1440/repositories/sembast/sembast_repository.dart';

/// A message for database operations that can be sent to a background isolate
class DatabaseOperationMessage {
  final String operationType;
  final String storeName;
  final Map<String, dynamic>? data;
  final String? key;
  final bool isTransaction;
  final List<Map<String, dynamic>>? batchOperations;

  DatabaseOperationMessage({
    required this.operationType,
    required this.storeName,
    this.data,
    this.key,
    this.isTransaction = true,
    this.batchOperations,
  });

  Map<String, dynamic> toMap() {
    return {
      'operationType': operationType,
      'storeName': storeName,
      'data': data,
      'key': key,
      'isTransaction': isTransaction,
      'batchOperations': batchOperations,
    };
  }

  factory DatabaseOperationMessage.fromMap(Map<String, dynamic> map) {
    return DatabaseOperationMessage(
      operationType: map['operationType'],
      storeName: map['storeName'],
      data: map['data'],
      key: map['key'],
      isTransaction: map['isTransaction'] ?? true,
      batchOperations: map['batchOperations'],
    );
  }
}

/// Result of a database operation
class DatabaseOperationResult {
  final dynamic result;
  final String? error;
  final bool success;
  final String operationType;
  final String storeName;
  final int executionTimeMs;

  DatabaseOperationResult({
    this.result,
    this.error,
    required this.success,
    required this.operationType,
    required this.storeName,
    required this.executionTimeMs,
  });

  Map<String, dynamic> toMap() {
    return {
      'result': result,
      'error': error,
      'success': success,
      'operationType': operationType,
      'storeName': storeName,
      'executionTimeMs': executionTimeMs,
    };
  }

  factory DatabaseOperationResult.fromMap(Map<String, dynamic> map) {
    return DatabaseOperationResult(
      result: map['result'],
      error: map['error'],
      success: map['success'],
      operationType: map['operationType'],
      storeName: map['storeName'],
      executionTimeMs: map['executionTimeMs'],
    );
  }
}

/// Handles database operations in a background isolate
class DatabaseWorker {
  static Isolate? _isolate;
  static ReceivePort? _receivePort;
  static SendPort? _sendPort;
  static Completer<void>? _initCompleter;
  static final Map<int, Completer<DatabaseOperationResult>> _operations = {};
  static int _nextOperationId = 0;
  
  // Token for background isolate initialization
  static RootIsolateToken? _rootIsolateToken;
  
  // Event-based store change notification
  static final Map<String, bool> _registeredStores = {};
  
  // Stream controller for store change notifications
  static StreamController<Map<String, dynamic>> _storeChangeController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  /// Stream of store change notifications
  static Stream<Map<String, dynamic>> get storeChanges => _storeChangeController.stream;

  /// Initialize the database worker isolate
  static Future<void> init() async {
    if (_isolate != null) {
      return;
    }

    _initCompleter = Completer<void>();
    
    // Re-initialize store change controller if needed
    if (_storeChangeController.isClosed) {
      print('💬 DatabaseWorker: Re-creating store change controller');
      _storeChangeController = StreamController<Map<String, dynamic>>.broadcast();
    }

    // Only get RootIsolateToken if we need platform channel access
    // Most database operations don't require platform channels
    if (kIsWeb || Platform.isIOS || Platform.isAndroid) {
      // These platforms might need platform channel access for file system operations
      _rootIsolateToken = RootIsolateToken.instance;
      if (_rootIsolateToken != null) {
        // Only log success, don't log failures
        print('✅ DatabaseWorker: Successfully obtained RootIsolateToken');
      }
    }

    // Create a ReceivePort for receiving messages from the isolate
    _receivePort = ReceivePort();

    // Spawn the isolate with root isolate token
    try {
      _isolate = await Isolate.spawn(
        _isolateEntryPoint,
        [_receivePort!.sendPort, _rootIsolateToken],
      );
      print('[DATABASE_WORKER] Successfully spawned worker isolate with RootIsolateToken');
    } catch (e) {
      print('[DATABASE_WORKER] ❌ Error spawning isolate: $e');
      _initCompleter!.completeError(e);
      return;
    }

    // Listen for messages from the isolate
    _receivePort!.listen((message) {
      if (message is SendPort) {
        // Store the SendPort for sending messages to the isolate
        _sendPort = message;
        _initCompleter!.complete();
      } else if (message is Map<String, dynamic> && message['type'] == 'storeChange') {
        // Handle store change notifications from the isolate
        print('📣 DatabaseWorker (main isolate): Received store change notification for store: ${message['storeName']}');
        
        // Forward to the store change stream
        if (!_storeChangeController.isClosed) {
          _storeChangeController.add(message);
        }
      } else if (message is Map<String, dynamic>) {
        // Handle operation result
        final operationId = message['operationId'] as int? ?? -1;
        
        // Skip if there's no operationId (might be a special message)
        if (operationId < 0) return;
        
        final result = DatabaseOperationResult.fromMap(message['result']);

        // Complete the operation
        final completer = _operations[operationId];
        if (completer != null) {
          completer.complete(result);
          _operations.remove(operationId);
        }
      }
    });

    // Wait for the isolate to initialize
    await _initCompleter!.future;
  }

  /// Execute a database operation in the background isolate
  static Future<DatabaseOperationResult> execute(DatabaseOperationMessage message) async {
    if (_sendPort == null) {
      await init();
    }

    final completer = Completer<DatabaseOperationResult>();
    final operationId = _nextOperationId++;
    _operations[operationId] = completer;

    _sendPort!.send({
      'operationId': operationId,
      'message': message.toMap(),
    });

    return completer.future;
  }

  /// Dispose the database worker isolate
  static void dispose() {
    if (_isolate != null) {
      _isolate!.kill(priority: Isolate.immediate);
      _isolate = null;
    }

    if (_receivePort != null) {
      _receivePort!.close();
      _receivePort = null;
    }
    
    // Close the store change controller
    if (!_storeChangeController.isClosed) {
      _storeChangeController.close();
    }

    _sendPort = null;
    _initCompleter = null;
    _operations.clear();
    _nextOperationId = 0;
    _registeredStores.clear();
  }

  /// Entry point for the isolate
  static void _isolateEntryPoint(List<dynamic> args) async {
    final SendPort sendPort = args[0] as SendPort;
    final RootIsolateToken? rootIsolateToken = args.length > 1 ? args[1] as RootIsolateToken? : null;
    
    // Initialize the BackgroundIsolateBinaryMessenger if token is available
    if (rootIsolateToken != null) {
      try {
        print('[DATABASE_WORKER] Initializing BackgroundIsolateBinaryMessenger with RootIsolateToken');
        // Directly initialize without using compute (which would create another isolate)
        BackgroundIsolateBinaryMessenger.ensureInitialized(rootIsolateToken);
        print('[DATABASE_WORKER] ✅ Successfully initialized BackgroundIsolateBinaryMessenger');
      } catch (e) {
        print('[DATABASE_WORKER] ⚠️ Warning: Failed to initialize BackgroundIsolateBinaryMessenger: $e');
        // Continue anyway - this might not be critical depending on the operations
      }
    } else {
      // Skip trying to get a token in the background isolate
    // Only initialize if actually needed for platform operations
    if (kIsWeb || Platform.isIOS || Platform.isAndroid) {
      print('[DATABASE_WORKER] No token provided to background isolate, some platform operations may be limited');
    }
    }

    // Create a ReceivePort for receiving messages from the main isolate
    final receivePort = ReceivePort();

    // Send the SendPort to the main isolate
    sendPort.send(receivePort.sendPort);

    // Get the database with retry
    Database? database;
    int retryCount = 0;
    const maxRetries = 3;
    
    while (database == null && retryCount < maxRetries) {
      try {
        database = await SembastDatabase.instance.database;
        print('✅ Successfully initialized database in isolate');
      } catch (e) {
        retryCount++;
        print('⚠️ Error initializing database in isolate (attempt $retryCount/$maxRetries): $e');
        if (retryCount >= maxRetries) {
          print('❌ Failed to initialize database after $maxRetries attempts');
          sendPort.send({'error': 'Failed to initialize database: $e'});
          return;
        }
        await Future.delayed(Duration(milliseconds: 500 * retryCount));
      }
    }

    // Listen for messages from the main isolate
    receivePort.listen((message) async {
      // Handle special store change notification messages
      if (message is Map<String, dynamic> && message['type'] == 'storeChange') {
        // Forward store change notifications to the main isolate
        sendPort.send(message); // Send the notification directly
        return;
      }
      
      if (message is Map<String, dynamic>) {
        final operationId = message['operationId'] as int;
        final operationMessage = DatabaseOperationMessage.fromMap(message['message']);

        // Skip processing if database is null
        if (database == null) {
          sendPort.send({
            'operationId': operationId,
            'result': DatabaseOperationResult(
              error: 'Database is not initialized',
              success: false,
              operationType: operationMessage.operationType,
              storeName: operationMessage.storeName,
              executionTimeMs: 0,
            ).toMap(),
          });
          return;
        }

        // Execute the operation
        try {
          final stopwatch = Stopwatch()..start();
          final result = await _executeOperation(database, operationMessage, sendPort);
          stopwatch.stop();

          // Send the result back to the main isolate
          sendPort.send({
            'operationId': operationId,
            'result': DatabaseOperationResult(
              result: result,
              success: true,
              operationType: operationMessage.operationType,
              storeName: operationMessage.storeName,
              executionTimeMs: stopwatch.elapsedMilliseconds,
            ).toMap(),
          });
        } catch (e) {
          final stopwatch = Stopwatch()..start()..stop();

          // Send the error back to the main isolate
          sendPort.send({
            'operationId': operationId,
            'result': DatabaseOperationResult(
              error: e.toString(),
              success: false,
              operationType: operationMessage.operationType,
              storeName: operationMessage.storeName,
              executionTimeMs: stopwatch.elapsedMilliseconds,
            ).toMap(),
          });
        }
      }
    });
  }

  /// Execute a database operation
  static Future<dynamic> _executeOperation(Database database, DatabaseOperationMessage message, [SendPort? isolateSendPort]) async {
    try {
      // Special handling for stream update operations
      if (message.operationType == 'registerStreamUpdates') {
        if (message.storeName == 'all' && message.data != null && message.data!.containsKey('stores')) {
          final storeNames = List<String>.from(message.data!['stores']);
          
          // Initialize tracking for each store
          for (final storeName in storeNames) {
            _registeredStores[storeName] = true;
          }
          
          print('✅ DatabaseWorker: Registered stream updates for ${storeNames.length} stores');
          return {'registered': true, 'storeNames': storeNames};
        } else {
          throw Exception('registerStreamUpdates requires a data object with a "stores" array');
        }
      } else if (message.operationType == 'registerStoreListener') {
        // Register a store for change notifications
        if (message.storeName != 'all' && message.data == null) {
          throw Exception('registerStoreListener requires a storeName');
        }
        
        if (message.storeName == 'all' && message.data != null && message.data!.containsKey('stores')) {
          // Register multiple stores
          final storeNames = List<String>.from(message.data!['stores']);
          for (final storeName in storeNames) {
            _registeredStores[storeName] = true;
          }
          print('✅ DatabaseWorker: Registered listeners for ${storeNames.length} stores: $storeNames');
          return {'registered': true, 'storeNames': storeNames};
        } else {
          // Register a single store
          _registeredStores[message.storeName] = true;
          print('✅ DatabaseWorker: Registered listener for store: ${message.storeName}');
          return {'registered': true, 'storeName': message.storeName};
        }
      } else if (message.operationType == 'unregisterStoreListener') {
        // Unregister a store from change notifications
        if (message.storeName == 'all') {
          _registeredStores.clear();
          print('✅ DatabaseWorker: Unregistered all store listeners');
          return {'unregistered': true, 'all': true};
        } else {
          _registeredStores.remove(message.storeName);
          print('✅ DatabaseWorker: Unregistered listener for store: ${message.storeName}');
          return {'unregistered': true, 'storeName': message.storeName};
        }
      }
      
      // Regular operations
      final store = stringMapStoreFactory.store(message.storeName);
      
      // For mutations, track last modified to help with change detection
      var result;
      final trackChange = ['add', 'update', 'delete', 'deleteAll', 'deleteByKey', 'deleteByFilter', 'batch'].contains(message.operationType);

      // Execute the operation based on type
      switch (message.operationType) {
        case 'find':
          final records = await store.find(database);
          // Extract values from record snapshots to avoid type errors
          result = records.map((record) => record.value).toList();
          break;
          
        case 'findFirst':
          final record = await store.findFirst(database);
          // Extract value from record snapshot to avoid type errors
          result = record?.value;
          break;
          
        case 'add':
          // Add lastModified field for change tracking if tracking is enabled
          Map<String, dynamic> data = Map<String, dynamic>.from(message.data ?? {});
          if (trackChange && !data.containsKey('lastModified')) {
            data['lastModified'] = DateTime.now().toIso8601String();
          }
          
          if (message.isTransaction) {
            result = await database.transaction((txn) async {
              return await store.add(txn, data);
            });
          } else {
            result = await store.add(database, data);
          }
          break;
          
        case 'update':
          // Add lastModified field for change tracking if tracking is enabled
          Map<String, dynamic> data = Map<String, dynamic>.from(message.data ?? {});
          if (trackChange && !data.containsKey('lastModified')) {
            data['lastModified'] = DateTime.now().toIso8601String();
          }
          
          if (message.isTransaction) {
            result = await database.transaction((txn) async {
              return await store.update(txn, data, finder: Finder(filter: Filter.byKey(message.key)));
            });
          } else {
            result = await store.update(database, data, finder: Finder(filter: Filter.byKey(message.key)));
          }
          break;
          
        case 'delete':
          if (message.isTransaction) {
            result = await database.transaction((txn) async {
              return await store.delete(txn);
            });
          } else {
            result = await store.delete(database);
          }
          break;
          
        case 'deleteAll':
          if (message.isTransaction) {
            result = await database.transaction((txn) async {
              return await store.delete(txn);
            });
          } else {
            result = await store.delete(database);
          }
          break;
          
        case 'deleteByKey':
          if (message.isTransaction) {
            result = await database.transaction((txn) async {
              return await store.delete(txn, finder: Finder(filter: Filter.byKey(message.data?['key'] ?? message.key)));
            });
          } else {
            result = await store.delete(database, finder: Finder(filter: Filter.byKey(message.data?['key'] ?? message.key)));
          }
          break;
          
        case 'deleteByFilter':
          if (message.data == null || !message.data!.containsKey('field') || !message.data!.containsKey('value')) {
            throw Exception('Invalid filter parameters: field and value must be provided');
          }
          
          final fieldName = message.data!['field'];
          final fieldValue = message.data!['value'];
          
          if (message.isTransaction) {
            result = await database.transaction((txn) async {
              return await store.delete(
                txn, 
                finder: Finder(filter: Filter.equals(fieldName, fieldValue))
              );
            });
          } else {
            result = await store.delete(
              database, 
              finder: Finder(filter: Filter.equals(fieldName, fieldValue))
            );
          }
          break;
          
        case 'batch':
          if (message.batchOperations == null || message.batchOperations!.isEmpty) {
            throw Exception('Batch operations cannot be null or empty');
          }
          
          result = await database.transaction((txn) async {
            final results = <dynamic>[];
            for (final operation in message.batchOperations!) {
              final opType = operation['type'];
              final opStoreName = operation['storeName'];
              final opStore = stringMapStoreFactory.store(opStoreName);
              
              // Add lastModified for change tracking
              Map<String, dynamic>? opData;
              if (operation['data'] != null) {
                opData = Map<String, dynamic>.from(operation['data']);
                if (trackChange && opType == 'add' && !opData.containsKey('lastModified')) {
                  opData['lastModified'] = DateTime.now().toIso8601String();
                }
              }
              
              switch (opType) {
                case 'add':
                  results.add(await opStore.add(txn, opData!));
                  break;
                case 'update':
                  results.add(await opStore.update(txn, opData!, finder: Finder(filter: Filter.byKey(operation['key']))));
                  break;
                case 'delete':
                  results.add(await opStore.delete(txn));
                  break;
                case 'deleteByKey':
                  results.add(await opStore.delete(txn, finder: Finder(filter: Filter.byKey(operation['key']))));
                  break;
                default:
                  throw Exception('Unknown batch operation type: $opType');
              }
              
              // Notify about store changes for registered stores
              if (_registeredStores.containsKey(opStoreName)) {
                print('📢 DatabaseWorker: Store changed during batch operation: $opStoreName; Operation type: $opType; Key: ${operation['key']}; Data: $opData');
              }
            }
            return results;
          });
          break;
          
        default:
          throw Exception('Unknown operation type: ${message.operationType}');
      }
      
      // Notify about store changes for registered stores
      final operationTypesThatModifyData = [
        'add', 'update', 'delete', 'deleteAll', 'deleteByKey', 'deleteByFilter', 'batch'
      ];
      
      if (operationTypesThatModifyData.contains(message.operationType) && 
          _registeredStores.containsKey(message.storeName)) {
        // This operation has modified data in a registered store, notify listeners
        print('📢 DatabaseWorker: Notifying about change in store: ${message.storeName}');
        
        try {
          // Create a change notification message
          final changeNotification = {
            'type': 'storeChange',
            'storeName': message.storeName,
            'operationType': message.operationType,
            'timestamp': DateTime.now().toIso8601String()
          };
          
          // Add to the local notification queue
          if (!_storeChangeController.isClosed) {
            _storeChangeController.add(changeNotification);
          }
          
          // If we're in a background isolate, also send the notification to the main isolate
          if (isolateSendPort != null) {
            isolateSendPort.send(changeNotification);
          }
        } catch (e) {
          print('⚠️ DatabaseWorker: Error sending store change notification: $e');
        }
      }
      
      return result;
    } catch (e) {
      print('❌ DatabaseWorker error executing operation: $e');
      rethrow;
    }
  }
}
