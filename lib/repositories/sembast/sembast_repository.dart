// lib/repositories/storage/sembast_database.dart
import 'dart:io';
import 'dart:async';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast.dart';
import 'package:sembast/sembast_io.dart';
import 'package:sembast/sembast_memory.dart';
import 'package:sembast_web/sembast_web.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';

class SembastDatabase {
  // Singleton instance
  static final SembastDatabase _singleton = SembastDatabase._();
  static SembastDatabase get instance => _singleton;
  
  // In-memory database path for fallback
  static const String inMemoryDatabasePath = ':memory:';
  
  // Logger for database operations
  RemoteLogger? get _logger {
    if (GetIt.I.isRegistered<LoggingUseCase>()) {
      return GetIt.I<LoggingUseCase>().getRemoteLogger('SembastDB');
    }
    return null;
  }

  // Private constructor
  SembastDatabase._();

  // Database object
  Database? _database;

  // Database getter
  Future<Database> get database async {
    if (_database != null) return _database!;
    try {
      _database = await _initDatabase();
      return _database!;
    } catch (e) {
      print('❌ SembastDatabase error initializing database: $e');
      rethrow;
    }
  }

  // Initialize database
  Future<Database> _initDatabase() async {
    _logger?.info('📊 Initializing database');
    try {
      // Check if we're in an isolate that needs BackgroundIsolateBinaryMessenger
      // First, try to initialize the BackgroundIsolateBinaryMessenger if we're in a background isolate
      try {
        // Try to get the RootIsolateToken and initialize BackgroundIsolateBinaryMessenger if needed
        final rootIsolateToken = RootIsolateToken.instance;
        if (rootIsolateToken != null) {
          print('[DI] Successfully obtained RootIsolateToken');
          try {
            print('[COMPUTE] Initializing BackgroundIsolateBinaryMessenger with RootIsolateToken');
            BackgroundIsolateBinaryMessenger.ensureInitialized(rootIsolateToken);
            print('[COMPUTE] ✅ Successfully initialized BackgroundIsolateBinaryMessenger with RootIsolateToken');
          } catch (e) {
            print('[COMPUTE] ⚠️ Error initializing BackgroundIsolateBinaryMessenger: $e');
          }
        }

        // Now try to initialize Flutter bindings
        WidgetsFlutterBinding.ensureInitialized();
        _logger?.info('📊 WidgetsFlutterBinding successfully initialized');
      } catch (e) {
        // This likely means we're in a background isolate without proper initialization
        _logger?.info('⚠️ Unable to initialize WidgetsFlutterBinding, may be in background isolate: $e');
        // We'll continue anyway and let platform channel operations fail if necessary
      }
      
      if (kIsWeb) {
        _logger?.info('📊 Using web factory');
        final factory = databaseFactoryWeb;
        return await factory.openDatabase('x1440_database');
      } else {
        // Try to get documents directory, which might fail in background isolate without messenger
        Directory dir;
        String dbPath;
        
        try {
          dir = await getApplicationDocumentsDirectory();
          dbPath = join(dir.path, 'x1440_database.db');
          _logger?.info('📊 Using IO factory, path: $dbPath');

          // Test if we can write to this path before proceeding
          final testFile = File(dbPath);
          if (!await testFile.exists()) {
            try {
              await testFile.create(recursive: true);
              await testFile.delete();
              _logger?.info('✅ Path verified as writable: $dbPath');
            } catch (writeError) {
              _logger?.info('⚠️ Path not writable, will try alternatives: $writeError');
              throw writeError; // Throw to move to fallback paths
            }
          }
        } catch (e) {
          // Fall back to a temporary directory if we can't get/use application documents directory
          _logger?.info('⚠️ Failed to get/use documents directory, trying temporary directory: $e');
          try {
            // Try to use the temporary directory instead
            final tempDir = await getTemporaryDirectory();
            dbPath = join(tempDir.path, 'x1440_database.db');
            _logger?.info('📊 Using temp directory fallback, path: $dbPath');
            
            // Test if we can write to this path
            final testFile = File(dbPath);
            try {
              if (!await testFile.exists()) {
                await testFile.create(recursive: true);
                await testFile.delete();
                _logger?.info('✅ Temp path verified as writable: $dbPath');
              }
            } catch (writeError) {
              _logger?.info('⚠️ Temp path not writable, will try application support: $writeError');
              // Try application support directory as another option
              final appSupportDir = await getApplicationSupportDirectory();
              dbPath = join(appSupportDir.path, 'x1440_database.db');
              _logger?.info('📊 Using application support directory fallback, path: $dbPath');
            }
          } catch (e2) {
            // If all else fails, try using a cache directory or application storage directory
            _logger?.error('❌ Failed to get working directory: $e2');
            try {
              // Last resort - try to get the cache directory
              final cacheDir = await getApplicationCacheDirectory();
              dbPath = join(cacheDir.path, 'x1440_database.db');
              _logger?.info('📊 Using cache directory last-resort fallback, path: $dbPath');
            } catch (e3) {
              // If nothing works, use an in-memory database
              _logger?.error('❌ All paths failed, using in-memory database: $e3');
              // Use special in-memory database identifier
              dbPath = inMemoryDatabasePath;
              _logger?.info('📊 Using in-memory database as last resort');
            }
          }
        }
        
        final factory = databaseFactoryIo;
        try {
          // Special handling for in-memory database
          if (dbPath == inMemoryDatabasePath) {
            _logger?.info('✅ Opening in-memory database');
            // For in-memory database, use the memory factory
            final memoryFactory = databaseFactoryMemory;
            // Create a random database name for this session
            final randomDbName = 'x1440_memory_${DateTime.now().millisecondsSinceEpoch}';
            return await memoryFactory.openDatabase(randomDbName);
          } else {
            return await factory.openDatabase(dbPath);
          }
        } catch (e) {
          _logger?.error('❌ Error opening database: $e');
          // If all else fails, try one last time with in-memory DB
          try {
            _logger?.info('🔄 Last resort: Attempting in-memory database after all failures');
            final memoryFactory = databaseFactoryMemory;
            final lastResortDbName = 'x1440_memory_fallback_${DateTime.now().millisecondsSinceEpoch}';
            return await memoryFactory.openDatabase(lastResortDbName);
          } catch (finalError) {
            _logger?.error('❌ Fatal error: Even in-memory database failed: $finalError');
            rethrow;
          }
        }
      }
    } catch (e) {
      _logger?.error('❌ Init database error: $e');
      rethrow;
    }
  }

  // Close database
  Future<void> close() async {
    _logger?.info('📊 Closing database');
    try {
      final db = _database;
      if (db != null) {
        await db.close();
        _database = null;
        _logger?.info('📊 Database closed successfully');
      } else {
        _logger?.info('📊 Database was already null');
      }
    } catch (e) {
      _logger?.error('❌ Error closing database: $e');
      // Rethrow to maintain original behavior
      rethrow;
    }
  }
}

extension SembastModelExtension<T> on T {
  Map<String, dynamic> toSembast() {
    if (this is Map<String, dynamic>) {
      return this as Map<String, dynamic>;
    }
    // Use the existing toJson method for your freezed models
    if (this is Object && (this as dynamic).toJson is Function) {
      return (this as dynamic).toJson() as Map<String, dynamic>;
    }
    throw UnimplementedError(
        'Cannot convert ${this.runtimeType} to Sembast format');
  }
}

extension SembastRecordRefExtension on RecordRef {
  /// Get the ID of this record as an int
  int? get intKey => key as int?;
}

extension MapExtension on Map<String, dynamic> {
  /// Add the Sembast record ID to the map as 'id'
  Map<String, dynamic> withId(int id) {
    return {...this, 'id': id};
  }
}

/// Helper method to store Freezed models in Sembast
Map<String, dynamic> freezedToSembast(dynamic model) {
  if (model != null && model.toJson is Function) {
    return model.toJson() as Map<String, dynamic>;
  }
  throw Exception('Cannot convert ${model.runtimeType} to Sembast format');
}

/// Extension to add IDs to records when retrieving from Sembast
extension SembastRecordExtension<T> on Record {
  /// Convert a Sembast record to a model with the record's key as 'id'
  T toModel<T>(T Function(Map<String, dynamic>) fromJson) {
    // In Sembast 3.x, we need to cast the record to RecordSnapshot for type safety
    final recordMap = (this as RecordSnapshot<dynamic, dynamic>).value;
    if (recordMap is! Map) {
      throw Exception('Record value is not a Map: $recordMap');
    }
    
    final jsonWithId = Map<String, dynamic>.from(recordMap);
    // Get the key from RecordSnapshot
    final recordKey = (this as RecordSnapshot<dynamic, dynamic>).key;
    if (recordKey != null) {
      jsonWithId['id'] = recordKey;
    }
    return fromJson(jsonWithId);
  }
}
