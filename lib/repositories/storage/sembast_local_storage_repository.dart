import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:sembast/sembast.dart';
import 'package:x1440/api/dtos/log_event.dart';
import 'package:x1440/api/dtos/messaging_channel_entry.dart';
import 'package:x1440/frameworks/settings/models/app_local_settings.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/repositories/models/active_presence_status.dart';
import 'package:x1440/repositories/sembast/database_performance_tracker.dart';
import 'package:x1440/repositories/sembast/database_worker.dart';
import 'package:x1440/repositories/sembast/sembast_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/demo/demo_user.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/models/previously_logged_in_user.dart';
import 'package:x1440/use_cases/models/scrt_credentials.dart';

class SembastLocalStorageRepository implements LocalStorageRepository {
  RemoteLogger? get _logger {
    if (!GetIt.I.isRegistered<LoggingUseCase>()) {
      return null;
    }
    return GetIt.I<LoggingUseCase>().getRemoteLogger('SembastLocalStorage');
  }

  // Flag to control logging verbosity
  bool _verboseLogging = false; // Set to false by default to reduce log spam

  // Helper method for conditional logging
  void _logInfo(String message) {
    if (_verboseLogging) {
      _logger?.info(message);
    }
  }

  // Database performance tracking
  late DatabasePerformanceTracker _performanceTracker;

  // Constructor with no required parameters to break circular dependency
  SembastLocalStorageRepository();

  // Stores
  final _credentialsStore = stringMapStoreFactory.store('credentials');
  final _scrtCredentialsStore = stringMapStoreFactory.store('scrt_credentials');
  final _previouslyLoggedInUserStore =
      stringMapStoreFactory.store('previously_logged_in_user');
  final _appLocalSettingsStore =
      stringMapStoreFactory.store('app_local_settings');
  final _activePresenceStatusStore =
      stringMapStoreFactory.store('active_presence_status');
  final _queueReceiveMessagesStore =
      stringMapStoreFactory.store('queue_receive_messages');
  final _queueSendMessagesStore =
      stringMapStoreFactory.store('queue_send_messages');
  final _logEventsStore = stringMapStoreFactory.store('log_events');
  final _messagingChannelStore =
      stringMapStoreFactory.store('messaging_channel');
  final _demoUserStore = stringMapStoreFactory.store('demo_user');

  // Stream controllers
  final _credentialsStreamController =
      StreamController<Credentials>.broadcast();
  final _scrtCredentialsStreamController =
      StreamController<ScrtCredentials>.broadcast();
  final _appLocalSettingsStreamController =
      StreamController<AppLocalSettings>.broadcast();
  final _activePresenceStatusStream =
      StreamController<ActivePresenceStatus>.broadcast();
  final _messageReceiveQueueStreamController =
      StreamController<List<QueueReceiveMessage>>.broadcast();
  final _messageSendQueueStreamController =
      StreamController<List<QueueSendMessage>>.broadcast();

  // Database getter
  Future<Database> get _db => SembastDatabase.instance.database;

  // Progressive initialization strategy to avoid UI blocking
  Future<void> init() async {
    // Start database worker initialization but don't await it yet
    final dbWorkerInit = DatabaseWorker.init();

    // Initialize performance tracker with fallback if GetIt isn't ready
    RemoteLogger? fallbackLogger;
    try {
      if (GetIt.I.isRegistered<LoggingUseCase>()) {
        fallbackLogger =
            GetIt.I<LoggingUseCase>().getRemoteLogger('SembastLocalStorage');
      }
    } catch (e) {
      print('⚠️ Warning: Unable to get logger from GetIt: $e');
    }
    _performanceTracker = DatabasePerformanceTracker(_logger ?? fallbackLogger);

    // Now complete database worker initialization
    await dbWorkerInit;
    _logger?.info('🚀 SembastLocalStorage: Database worker initialized');

    // Initialize streams asynchronously - don't await to prevent blocking
    // This allows the UI to become responsive immediately
    _initStreams().then((_) {
      _logger?.info(
          '✅ SembastLocalStorage: Stream initialization completed asynchronously');
    }).catchError((e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error in async stream initialization: $e');
    });
  }

  // Enable verbose logging only during development/testing
  void setVerboseLogging(bool verbose) {
    _verboseLogging = verbose;
    _logger?.info(
        'SembastLocalStorage: Verbose logging ${verbose ? 'enabled' : 'disabled'}');
  }

  Future<void> _initStreams() async {
    _logger?.info('🔌 SembastLocalStorage: Initializing database streams');
    try {
      // Setup critical stores first
      // final criticalStores = [
      //   'credentials',
      //   'app_local_settings'
      // ];

      // Register critical store listeners first for fast UI responsiveness
      // await DatabaseWorker.execute(DatabaseOperationMessage(
      //   operationType: 'registerStoreListener',
      //   storeName: 'all',
      //   data: {
      //     'stores': criticalStores,
      //   },
      //   isTransaction: false, // Not a transaction operation
      // ));

      // Then register non-critical stores asynchronously
      Future.microtask(() async {
        try {
          await DatabaseWorker.execute(DatabaseOperationMessage(
            operationType: 'registerStoreListener',
            storeName: 'all',
            data: {
              'stores': [
                'credentials',
                'app_local_settings'
                    'scrt_credentials',
                'active_presence_status',
                'queue_receive_messages',
                'queue_send_messages',
              ],
            },
            isTransaction: false, // Not a transaction operation
          ));
          _logInfo(
              '✅ SembastLocalStorage: Non-critical store listeners registered');
        } catch (e) {
          _logger?.error(
              '❌ SembastLocalStorage: Error registering non-critical store listeners: $e');
        }
      });

      // Use a rate-limited listener to prevent excessive processing
      var lastProcessTime = <String, DateTime>{};
      var processingQueue = <String, bool>{};

      // Subscribe to store change notifications with debouncing
      DatabaseWorker.storeChanges.listen((notification) async {
        try {
          final storeName = notification['storeName'] as String;
          final now = DateTime.now();

          // Debounce rapid notifications for the same store (minimum 100ms between updates)
          if (lastProcessTime.containsKey(storeName)) {
            final timeSinceLastProcess =
                now.difference(lastProcessTime[storeName]!);
            if (timeSinceLastProcess.inMilliseconds < 100) {
              _logInfo(
                  '⏱️ SembastLocalStorage: Debouncing rapid notification for $storeName');
              return;
            }
          }

          // Prevent concurrent processing of the same store
          if (processingQueue[storeName] == true) {
            _logInfo(
                '⏱️ SembastLocalStorage: Already processing $storeName, skipping notification');
            return;
          }

          // Mark as processing
          processingQueue[storeName] = true;
          _logger?.info(
              '📢 SembastLocalStorage: Received store change notification for $storeName');

          try {
            // Process each store change notification
            switch (storeName) {
              case 'credentials':
                _credentialsStreamController.add(await getCredentials());
                break;
              case 'scrt_credentials':
                _scrtCredentialsStreamController
                    .add(await getScrtCredentials());
                break;
              case 'app_local_settings':
                final settings = await getAppLocalSettings();
                _appLocalSettingsStreamController.add(settings);
                break;
              case 'active_presence_status':
                _activePresenceStatusStream
                    .add(await getActivePresenceStatus());
                break;
              case 'queue_receive_messages':
                final messages = await getMessageReceiveQueueState();
                _logInfo(
                    '💬 SembastLocalStorage: Emitting ${messages.length} receive queue messages from notification');
                _messageReceiveQueueStreamController.add(messages);
                break;
              case 'queue_send_messages':
                final messages = await getMessageSendQueueState();
                _logInfo(
                    '💬 SembastLocalStorage: Emitting ${messages.length} send queue messages from notification');
                _messageSendQueueStreamController.add(messages);
                break;
            }

            // Update last process time
            lastProcessTime[storeName] = now;
          } finally {
            // Mark as not processing anymore
            processingQueue[storeName] = false;
          }
        } catch (e) {
          _logger?.error(
              '❌ SembastLocalStorage: Error handling store change notification: $e');
        }
      });

      _logger?.info(
          '✅ SembastLocalStorage: Database streams initialized successfully');
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error initializing database streams: $e');
      // Don't rethrow since this is initialization - we'll log and continue
    }
  }

  // Stream getters implementation
  @override
  Stream<Credentials> get credentialsStream =>
      _credentialsStreamController.stream;

  @override
  Stream<AppLocalSettings> get appLocalSettingsStream =>
      _appLocalSettingsStreamController.stream;

  @override
  Stream<List<QueueReceiveMessage>> get messageReceiveQueueStream =>
      _messageReceiveQueueStreamController.stream;

  @override
  Stream<List<QueueSendMessage>> get messageSendQueueStream =>
      _messageSendQueueStreamController.stream;

  // Implement credentials methods
  @override
  Future<void> clearCredentials() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'delete',
        storeName: 'credentials',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing credentials: ${result.error}');
        throw Exception('Failed to clear credentials: ${result.error}');
      }
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error clearing credentials: $e');
      // Rethrow to inform caller of the error
      rethrow;
    }
  }

  @override
  Future<Credentials> getCredentials() async {
    try {
      _logInfo('🔍 SembastLocalStorage: Getting credentials');
      // Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'findFirst',
        storeName: 'credentials',
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting credentials: ${result.error}');
        throw Exception('Failed to get credentials: ${result.error}');
      }

      return result.result != null
          ? Credentials.fromJson(result.result)
          : const Credentials(accessToken: null);
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error getting credentials: $e');
      // On error, return empty credentials instead of crashing the app
      return const Credentials(accessToken: null);
    }
  }

  @override
  Future<void> setCredentials(Credentials credentials) async {
    try {
      _logInfo('💾 SembastLocalStorage: Saving credentials');
      // Execute batch operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'batch',
        storeName: 'credentials', // Not used for batch but required
        isTransaction: true,
        batchOperations: [
          {
            'type': 'delete',
            'storeName': 'credentials',
          },
          {
            'type': 'add',
            'storeName': 'credentials',
            'data': credentials.toJson(),
          },
        ],
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error saving credentials: ${result.error}');
        throw Exception('Failed to save credentials: ${result.error}');
      }

      // Emit the updated credentials
      _credentialsStreamController.add(credentials);
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error saving credentials: $e');
      // Rethrow to inform caller of the error
      rethrow;
    }
  }

  // Implement SCRT credentials methods
  @override
  Future<ScrtCredentials> getScrtCredentials() async {
    try {
      _logInfo('🔍 SembastLocalStorage: Getting SCRT credentials');
      // Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'findFirst',
        storeName: 'scrt_credentials',
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting SCRT credentials: ${result.error}');
        throw Exception('Failed to get SCRT credentials: ${result.error}');
      }
      return result.result != null
          ? ScrtCredentials.fromJson(result.result)
          : ScrtCredentials();
    } catch (e) {
      _logger
          ?.error('❌ SembastLocalStorage: Error getting SCRT credentials: $e');
      // On error, return empty credentials instead of crashing the app
      return ScrtCredentials();
    }
  }

  @override
  Future<void> setScrtCredentials(ScrtCredentials credentials) async {
    try {
      _logInfo('💾 SembastLocalStorage: Saving SCRT credentials');
      // Execute batch operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'batch',
        storeName: 'scrt_credentials', // Not used for batch but required
        isTransaction: true,
        batchOperations: [
          {
            'type': 'delete',
            'storeName': 'scrt_credentials',
          },
          {
            'type': 'add',
            'storeName': 'scrt_credentials',
            'data': credentials.toJson(),
          },
        ],
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error saving SCRT credentials: ${result.error}');
        throw Exception('Failed to save SCRT credentials: ${result.error}');
      }

      // Emit the updated credentials
      _scrtCredentialsStreamController.add(credentials);
    } catch (e) {
      _logger
          ?.error('❌ SembastLocalStorage: Error saving SCRT credentials: $e');
      // Rethrow to inform caller of the error
      rethrow;
    }
  }

  @override
  Future<void> clearScrtCredentials() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'delete',
        storeName: 'scrt_credentials',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing SCRT credentials: ${result.error}');
        throw Exception('Failed to clear SCRT credentials: ${result.error}');
      }
    } catch (e) {
      _logger
          ?.error('❌ SembastLocalStorage: Error clearing SCRT credentials: $e');
      // Rethrow to inform caller of the error
      rethrow;
    }
  }

  // Implement app local settings methods
  @override
  Future<void> clearAppLocalSettings() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'delete',
        storeName: 'app_local_settings',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing app settings: ${result.error}');
        throw Exception('Failed to clear app settings: ${result.error}');
      }
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error clearing app settings: $e');
      rethrow;
    }
  }

  @override
  Future<AppLocalSettings> getAppLocalSettings() async {
    try {
      // Use background isolate for database operation
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'findFirst',
        storeName: 'app_local_settings',
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting app settings: ${result.error}');
        throw Exception('Failed to get app settings: ${result.error}');
      }
      final settings = result.result != null
          ? AppLocalSettings.fromJson(result.result)
          : AppLocalSettings();

      return settings;
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error getting app settings: $e');
      // Return default settings on error to avoid crashing the app
      return AppLocalSettings();
    }
  }

  @override
  Future<void> setAppLocalSettings(AppLocalSettings appLocalSettings) async {
    try {
      // Execute batch operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'batch',
        storeName: 'app_local_settings', // Not used for batch but required
        isTransaction: true,
        batchOperations: [
          {
            'type': 'delete',
            'storeName': 'app_local_settings',
          },
          {
            'type': 'add',
            'storeName': 'app_local_settings',
            'data': appLocalSettings.toJson(),
          },
        ],
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error saving app settings: ${result.error}');
        throw Exception('Failed to save app settings: ${result.error}');
      }
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error saving app settings: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearActivePresenceStatus() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'delete',
        storeName: 'active_presence_status',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing active presence status: ${result.error}');
        throw Exception(
            'Failed to clear active presence status: ${result.error}');
      }
      _activePresenceStatusStream.add(const ActivePresenceStatus());
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error clearing active presence status: $e');
      rethrow;
    }
  }

  @override
  Future<void> setActivePresenceStatus(ActivePresenceStatus status) async {
    try {
      // Execute batch operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'batch',
        storeName: 'active_presence_status', // Not used for batch but required
        isTransaction: true,
        batchOperations: [
          {
            'type': 'delete',
            'storeName': 'active_presence_status',
          },
          {
            'type': 'add',
            'storeName': 'active_presence_status',
            'data': status.toJson(),
          },
        ],
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error saving presence status: ${result.error}');
        throw Exception('Failed to save presence status: ${result.error}');
      }
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error saving presence status: $e');
      // Rethrow to inform caller of the error
      rethrow;
    }
  }

  @override
  Future<ActivePresenceStatus> getActivePresenceStatus() async {
    try {
      // Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'findFirst',
        storeName: 'active_presence_status',
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting presence status: ${result.error}');
        throw Exception('Failed to get presence status: ${result.error}');
      }

      return result.result != null
          ? ActivePresenceStatus.fromJson(result.result)
          : ActivePresenceStatus();
    } catch (e) {
      _logger
          ?.error('❌ SembastLocalStorage: Error getting presence status: $e');
      // Return default status instead of crashing
      return ActivePresenceStatus();
    }
  }

  // Implement message receive queue methods
  @override
  Future<List<QueueReceiveMessage>> getMessageReceiveQueueState() async {
    try {
      // Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'find',
        storeName: 'queue_receive_messages',
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting message receive queue: ${result.error}');
        throw Exception('Failed to get message receive queue: ${result.error}');
      }

      final messages = (result.result as List<dynamic>)
          .where((record) => record != null)
          .map((record) {
        // Handle both raw values and Sembast record snapshots
        final recordData = record is Map && record.containsKey('value')
            ? record['value'] as Map<String, dynamic>
            : record as Map<String, dynamic>;
        return QueueReceiveMessage.fromJson(recordData);
      }).toList();

      return messages;
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error getting message receive queue: $e');
      // Return empty list on error instead of crashing
      return [];
    }
  }

  @override
  Future<void> clearReceiveQueue() async {
    try {
      // Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'deleteAll',
        storeName: 'queue_receive_messages',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing receive queue: ${result.error}');
        throw Exception('Failed to clear receive queue: ${result.error}');
      }

      _logger?.info('✅ SembastLocalStorage: Receive queue cleared');
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error clearing receive queue: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearSendQueue() async {
    try {
      // Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'deleteAll',
        storeName: 'queue_send_messages',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing send queue: ${result.error}');
        throw Exception('Failed to clear send queue: ${result.error}');
      }
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error clearing send queue: $e');
      rethrow;
    }
  }



@override
  Future<void> addMessageToReceiveQueue(
      QueueReceiveMessage queueReceiveMessage) async {
    try {
      // Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'add',
        storeName: 'queue_receive_messages',
        data: queueReceiveMessage.toJson(),
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error adding message to receive queue: ${result.error}');
        throw Exception(
            'Failed to add message to receive queue: ${result.error}');
      }
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error adding message to receive queue: $e');
      rethrow;
    }
  }

@override
Future<void> removeMessageFromReceiveQueueByNotificationId(
    String notificationId) async {
  try {
    // First approach: Use a custom operation to delete messages by filter
    final result = await DatabaseWorker.execute(DatabaseOperationMessage(
      operationType: 'deleteByFilter',
      storeName: 'queue_receive_messages',
      data: {'field': 'notificationId', 'value': notificationId},
      isTransaction: true,
    ));

    // Track performance
    _performanceTracker.trackOperation(result);

    if (!result.success) {
      _logger?.warn(
          '⚠️ SembastLocalStorage: Could not use deleteByFilter, falling back to alternative approach: ${result.error}');

      // Fallback: Execute a delete after finding records that match the notificationId
      final findResult =
          await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'find',
        storeName: 'queue_receive_messages',
        isTransaction: false,
      ));

      _performanceTracker.trackOperation(findResult);

      if (!findResult.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error finding messages in queue: ${findResult.error}');
        throw Exception(
            'Failed to find messages in queue: ${findResult.error}');
      }

      // Extract records and filter for the notificationId
      final records = (findResult.result as List<dynamic>)
          .where((record) => record != null)
          .map((record) {
            // Convert to a consistent format regardless of whether we get a RecordSnapshot or a Map
            final recordData = record is Map && record.containsKey('value')
                ? record['value'] as Map<String, dynamic>
                : record as Map<String, dynamic>;

            // Check if this is a snapshot with a key
            final key = record is Map && record.containsKey('key')
                ? record['key']
                : null;

            return {
              'data': recordData,
              'key': key,
              'matchesFilter': recordData['notificationId'] == notificationId
            };
          })
          .where((record) => record['matchesFilter'] == true)
          .toList();

      // If we found matching records with keys, delete them
      if (records.isNotEmpty) {
        _logger?.info(
            '🔍 SembastLocalStorage: Found ${records.length} matching messages to delete');

        // If we have keys, use them for deletion
        final recordsWithKeys =
            records.where((r) => r['key'] != null).toList();
        if (recordsWithKeys.isNotEmpty) {
          for (final record in recordsWithKeys) {
            final deleteResult =
                await DatabaseWorker.execute(DatabaseOperationMessage(
              operationType: 'deleteByKey',
              storeName: 'queue_receive_messages',
              data: {'key': record['key']},
              isTransaction: true,
            ));

            _performanceTracker.trackOperation(deleteResult);
            if (!deleteResult.success) {
              _logger?.error(
                  '❌ SembastLocalStorage: Error deleting message by key: ${deleteResult.error}');
            }
          }
        } else {
          // Last resort: Fall back to direct database access
          _logger?.warn(
              '⚠️ SembastLocalStorage: No keys found for records, falling back to direct DB access');
          final db = await _db;
          final finder =
              Finder(filter: Filter.equals('notificationId', notificationId));
          await db.transaction((txn) async {
            final store = stringMapStoreFactory.store('queue_receive_messages');
            await store.delete(txn, finder: finder);
          });
        }
      }
    }

    // Verify and update listeners after removal
    final queueState = await getMessageReceiveQueueState();
    _messageReceiveQueueStreamController.add(queueState);

    _logger?.info(
        '✅ SembastLocalStorage: Message removed from receive queue. Remaining: ${queueState.length}');
  } catch (e) {
    _logger?.error(
        '❌ SembastLocalStorage: Error removing message from receive queue: $e');
    rethrow;
  }
}

  @override
  Future<void> removeMessageFromSendQueueByMessageId(String messageId) async {
    try {
      // First approach: Use a custom operation to delete messages by filter
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'deleteByFilter',
        storeName: 'queue_send_messages',
        data: {'field': 'messageId', 'value': messageId},
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.warn(
            '⚠️ SembastLocalStorage: Could not use deleteByFilter, falling back to alternative approach: ${result.error}');

        // Fallback: Execute a delete after finding records that match the messageId
        final findResult =
            await DatabaseWorker.execute(DatabaseOperationMessage(
          operationType: 'find',
          storeName: 'queue_send_messages',
          isTransaction: false,
        ));

        _performanceTracker.trackOperation(findResult);

        if (!findResult.success) {
          _logger?.error(
              '❌ SembastLocalStorage: Error finding messages in queue: ${findResult.error}');
          throw Exception(
              'Failed to find messages in queue: ${findResult.error}');
        }

        // Extract records and filter for the messageId
        final records = (findResult.result as List<dynamic>)
            .where((record) => record != null)
            .map((record) {
              // Convert to a consistent format regardless of whether we get a RecordSnapshot or a Map
              final recordData = record is Map && record.containsKey('value')
                  ? record['value'] as Map<String, dynamic>
                  : record as Map<String, dynamic>;

              // Check if this is a snapshot with a key
              final key = record is Map && record.containsKey('key')
                  ? record['key']
                  : null;

              return {
                'data': recordData,
                'key': key,
                'matchesFilter': recordData['messageId'] == messageId
              };
            })
            .where((record) => record['matchesFilter'] == true)
            .toList();

        // If we found matching records with keys, delete them
        if (records.isNotEmpty) {
          _logger?.info(
              '🔍 SembastLocalStorage: Found ${records.length} matching messages to delete');

          // If we have keys, use them for deletion
          final recordsWithKeys =
              records.where((r) => r['key'] != null).toList();
          if (recordsWithKeys.isNotEmpty) {
            for (final record in recordsWithKeys) {
              final deleteResult =
                  await DatabaseWorker.execute(DatabaseOperationMessage(
                operationType: 'deleteByKey',
                storeName: 'queue_send_messages',
                data: {'key': record['key']},
                isTransaction: true,
              ));

              _performanceTracker.trackOperation(deleteResult);
              if (!deleteResult.success) {
                _logger?.error(
                    '❌ SembastLocalStorage: Error deleting message by key: ${deleteResult.error}');
              }
            }
          } else {
            // Last resort: Fall back to direct database access
            _logger?.warn(
                '⚠️ SembastLocalStorage: No keys found for records, falling back to direct DB access');
            final db = await _db;
            final finder =
                Finder(filter: Filter.equals('messageId', messageId));
            await db.transaction((txn) async {
              final store = stringMapStoreFactory.store('queue_send_messages');
              await store.delete(txn, finder: finder);
            });
          }
        }
      }

      // Verify and update listeners after removal
      final queueState = await getMessageSendQueueState();
      _messageSendQueueStreamController.add(queueState);

      _logger?.info(
          '✅ SembastLocalStorage: Message removed from send queue. Remaining: ${queueState.length}');
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error removing message from send queue: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearLogs() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'delete',
        storeName: 'log_events',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing logs: ${result.error}');
        throw Exception('Failed to clear logs: ${result.error}');
      }
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error clearing logs: $e');
      rethrow;
    }
  }

  @override
  Future<List<LogEvent>> getLogs() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'find',
        storeName: 'log_events',
        isTransaction: false, // Read operation doesn't need transaction
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting logs: ${result.error}');
        return [];
      }

      // Extract values from record snapshots to avoid type errors
      final logs = (result.result as List<dynamic>)
          .where((record) => record != null)
          .map((record) {
        final recordData = record is Map && record.containsKey('value')
            ? record['value'] as Map<String, dynamic>
            : record as Map<String, dynamic>;
        return LogEvent.fromJson(recordData);
      }).toList();

      return logs;
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error getting logs: $e');
      // Return empty list on error instead of crashing
      return [];
    }
  }

  @override
  Future<void> addLogEvent(LogEvent log) async {
    if (log.message.isEmpty) return;
    try {
      // Execute operation in background isolate without logging or performance tracking (to avoid recursion)
      await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'add',
        storeName: 'log_events',
        data: log.toJson(),
        isTransaction: true,
      ));
    } catch (e) {
      // Don't log here to avoid recursion
      print('❌ SembastLocalStorage: Error adding log event: $e');
    }
  }

  // Implement previously logged in user methods
  @override
  Future<void> clearPreviouslyLoggedInUser() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'delete',
        storeName: 'previously_logged_in_user',
        isTransaction: true,
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing previously logged in user: ${result.error}');
        throw Exception(
            'Failed to clear previously logged in user: ${result.error}');
      }
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error clearing previously logged in user: $e');
      rethrow;
    }
  }

  @override
  Future<PreviouslyLoggedInUser?> getPreviouslyLoggedInUser() async {
    try {
      // Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'findFirst',
        storeName: 'previously_logged_in_user',
        isTransaction: false, // Read operation doesn't need transaction
      ));

      // Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting previously logged in user: ${result.error}');
        return null;
      }

      return result.result != null
          ? PreviouslyLoggedInUser.fromJson(result.result)
          : null;
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error getting previously logged in user: $e');
// Return null on error to avoid crashing
      return null;
    }
  }

  @override
  Future<void> setPreviouslyLoggedInUser(
      PreviouslyLoggedInUser previouslyLoggedInUser) async {
    try {
// Execute batch operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'batch',
        storeName: 'previously_logged_in_user',
        // Not used for batch but required
        isTransaction: true,
        batchOperations: [
          {
            'type': 'delete',
            'storeName': 'previously_logged_in_user',
          },
          {
            'type': 'add',
            'storeName': 'previously_logged_in_user',
            'data': previouslyLoggedInUser.toJson(),
          },
        ],
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error saving previously logged in user: ${result.error}');
        throw Exception(
            'Failed to save previously logged in user: ${result.error}');
      }
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error saving previously logged in user: $e');
      rethrow;
    }
  }

// Implement messaging channel methods
  @override
  Future<void> clearMessagingChannel() async {
    try {
// Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'delete',
        storeName: 'messaging_channel',
        isTransaction: true,
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error clearing messaging channel: ${result.error}');
        throw Exception('Failed to clear messaging channel: ${result.error}');
      }
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error clearing messaging channel: $e');
      rethrow;
    }
  }

  @override
  Future<MessagingChannelEntry?> getMessagingChannel() async {
    try {
// Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'findFirst',
        storeName: 'messaging_channel',
        isTransaction: false, // Read operation doesn't need transaction
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting messaging channel: ${result.error}');
        return null;
      }

      return result.result != null
          ? MessagingChannelEntry.fromJson(result.result)
          : null;
    } catch (e) {
      _logger
          ?.error('❌ SembastLocalStorage: Error getting messaging channel: $e');
// Return null on error to avoid crashing
      return null;
    }
  }

  @override
  Future<void> setMessagingChannel(MessagingChannelEntry channel) async {
    try {
// Execute batch operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'batch',
        storeName: 'messaging_channel', // Not used for batch but required
        isTransaction: true,
        batchOperations: [
          {
            'type': 'delete',
            'storeName': 'messaging_channel',
          },
          {
            'type': 'add',
            'storeName': 'messaging_channel',
            'data': channel.toJson(),
          },
        ],
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error saving messaging channel: ${result.error}');
        throw Exception('Failed to save messaging channel: ${result.error}');
      }
    } catch (e) {
      _logger
          ?.error('❌ SembastLocalStorage: Error saving messaging channel: $e');
      rethrow;
    }
  }

  @override
  Future<List<QueueSendMessage>> getMessageSendQueueState() async {
    try {
// Use the database worker to execute the operation in a background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'find',
        storeName: 'queue_send_messages',
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting message send queue: ${result.error}');
        throw Exception('Failed to get message send queue: ${result.error}');
      }

// Extract values from Sembast record snapshots - similar to getMessageReceiveQueueState fix
      final messages = (result.result as List<dynamic>)
          .where((record) => record != null)
          .map((record) {
// Handle both raw values and Sembast record snapshots
        final recordData = record is Map && record.containsKey('value')
            ? record['value'] as Map<String, dynamic>
            : record as Map<String, dynamic>;
        return QueueSendMessage.fromJson(recordData);
      }).toList();

      return messages;
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error getting message send queue: $e');
// Return empty list on error instead of crashing
      return [];
    }
  }

  @override
  Future<void> addMessageToSendQueue(QueueSendMessage queueSendMessage) async {
    try {
// Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'add',
        storeName: 'queue_send_messages',
        data: queueSendMessage.toJson(),
        isTransaction: true,
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error adding message to send queue: ${result.error}');
        throw Exception('Failed to add message to send queue: ${result.error}');
      }
    } catch (e) {
      _logger?.error(
          '❌ SembastLocalStorage: Error adding message to send queue: $e');
      rethrow;
    }
  }

// Implement demo user methods
  @override
  Future<DemoUser?> getDemoUser() async {
    _logInfo('🔍 SembastLocalStorage: Getting demo user');
    try {
// Execute operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'findFirst',
        storeName: 'demo_user',
        isTransaction: false, // Read operation doesn't need transaction
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error getting demo user: ${result.error}');
        return null;
      }

      return result.result != null ? DemoUser.fromJson(result.result) : null;
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error getting demo user: $e');
// Return null on error to avoid crashing
      return null;
    }
  }

  @override
  Future<void> setDemoUser(DemoUser user) async {
    _logger?.info('💾 SembastLocalStorage: Saving demo user');
    try {
// Execute batch operation in background isolate
      final result = await DatabaseWorker.execute(DatabaseOperationMessage(
        operationType: 'batch',
        storeName: 'demo_user', // Not used for batch but required
        isTransaction: true,
        batchOperations: [
          {
            'type': 'delete',
            'storeName': 'demo_user',
          },
          {
            'type': 'add',
            'storeName': 'demo_user',
            'data': user.toJson(),
          },
        ],
      ));

// Track performance
      _performanceTracker.trackOperation(result);

      if (!result.success) {
        _logger?.error(
            '❌ SembastLocalStorage: Error saving demo user: ${result.error}');
        throw Exception('Failed to save demo user: ${result.error}');
      }
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error saving demo user: $e');
      rethrow;
    }
  }

// Dispose method to close streams
  void dispose() {
    _logger?.info('🚪 SembastLocalStorage: Disposing repository');
    try {
// Log performance summary before closing
      _performanceTracker.logPerformanceSummary();

// Close stream controllers
      _credentialsStreamController.close();
      _scrtCredentialsStreamController.close();
      _appLocalSettingsStreamController.close();
      _activePresenceStatusStream.close();
      _messageReceiveQueueStreamController.close();
      _messageSendQueueStreamController.close();

// Dispose database worker
      DatabaseWorker.dispose();

      _logger?.info('✅ SembastLocalStorage: Repository disposed successfully');
    } catch (e) {
      _logger?.error('❌ SembastLocalStorage: Error during dispose: $e');
// Don't rethrow since this is cleanup code
    }
  }
}
