// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pending_outbound_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PendingOutboundMessage {
  String? get contactId; // String? workTargetId,
  String get messageId;
  String? get messageContent; // String? messagingEndUserId,
  @FileToPathConverter()
  List<String> get filePathsToAttach;

  /// Create a copy of PendingOutboundMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PendingOutboundMessageCopyWith<PendingOutboundMessage> get copyWith =>
      _$PendingOutboundMessageCopyWithImpl<PendingOutboundMessage>(
          this as PendingOutboundMessage, _$identity);

  /// Serializes this PendingOutboundMessage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PendingOutboundMessage &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            const DeepCollectionEquality()
                .equals(other.filePathsToAttach, filePathsToAttach));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactId, messageId,
      messageContent, const DeepCollectionEquality().hash(filePathsToAttach));

  @override
  String toString() {
    return 'PendingOutboundMessage(contactId: $contactId, messageId: $messageId, messageContent: $messageContent, filePathsToAttach: $filePathsToAttach)';
  }
}

/// @nodoc
abstract mixin class $PendingOutboundMessageCopyWith<$Res> {
  factory $PendingOutboundMessageCopyWith(PendingOutboundMessage value,
          $Res Function(PendingOutboundMessage) _then) =
      _$PendingOutboundMessageCopyWithImpl;
  @useResult
  $Res call(
      {String? contactId,
      String messageId,
      String? messageContent,
      @FileToPathConverter() List<String> filePathsToAttach});
}

/// @nodoc
class _$PendingOutboundMessageCopyWithImpl<$Res>
    implements $PendingOutboundMessageCopyWith<$Res> {
  _$PendingOutboundMessageCopyWithImpl(this._self, this._then);

  final PendingOutboundMessage _self;
  final $Res Function(PendingOutboundMessage) _then;

  /// Create a copy of PendingOutboundMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = freezed,
    Object? messageId = null,
    Object? messageContent = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_self.copyWith(
      contactId: freezed == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageContent: freezed == messageContent
          ? _self.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _self.filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// Adds pattern-matching-related methods to [PendingOutboundMessage].
extension PendingOutboundMessagePatterns on PendingOutboundMessage {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PendingOutboundMessage value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PendingOutboundMessage() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PendingOutboundMessage value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PendingOutboundMessage():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PendingOutboundMessage value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PendingOutboundMessage() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? contactId,
            String messageId,
            String? messageContent,
            @FileToPathConverter() List<String> filePathsToAttach)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PendingOutboundMessage() when $default != null:
        return $default(_that.contactId, _that.messageId, _that.messageContent,
            _that.filePathsToAttach);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? contactId,
            String messageId,
            String? messageContent,
            @FileToPathConverter() List<String> filePathsToAttach)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PendingOutboundMessage():
        return $default(_that.contactId, _that.messageId, _that.messageContent,
            _that.filePathsToAttach);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? contactId,
            String messageId,
            String? messageContent,
            @FileToPathConverter() List<String> filePathsToAttach)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PendingOutboundMessage() when $default != null:
        return $default(_that.contactId, _that.messageId, _that.messageContent,
            _that.filePathsToAttach);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PendingOutboundMessage extends PendingOutboundMessage {
  const _PendingOutboundMessage(
      {this.contactId,
      required this.messageId,
      this.messageContent,
      @FileToPathConverter()
      final List<String> filePathsToAttach = const <String>[]})
      : _filePathsToAttach = filePathsToAttach,
        super._();
  factory _PendingOutboundMessage.fromJson(Map<String, dynamic> json) =>
      _$PendingOutboundMessageFromJson(json);

  @override
  final String? contactId;
// String? workTargetId,
  @override
  final String messageId;
  @override
  final String? messageContent;
// String? messagingEndUserId,
  final List<String> _filePathsToAttach;
// String? messagingEndUserId,
  @override
  @JsonKey()
  @FileToPathConverter()
  List<String> get filePathsToAttach {
    if (_filePathsToAttach is EqualUnmodifiableListView)
      return _filePathsToAttach;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filePathsToAttach);
  }

  /// Create a copy of PendingOutboundMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PendingOutboundMessageCopyWith<_PendingOutboundMessage> get copyWith =>
      __$PendingOutboundMessageCopyWithImpl<_PendingOutboundMessage>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PendingOutboundMessageToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PendingOutboundMessage &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            const DeepCollectionEquality()
                .equals(other._filePathsToAttach, _filePathsToAttach));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactId, messageId,
      messageContent, const DeepCollectionEquality().hash(_filePathsToAttach));

  @override
  String toString() {
    return 'PendingOutboundMessage(contactId: $contactId, messageId: $messageId, messageContent: $messageContent, filePathsToAttach: $filePathsToAttach)';
  }
}

/// @nodoc
abstract mixin class _$PendingOutboundMessageCopyWith<$Res>
    implements $PendingOutboundMessageCopyWith<$Res> {
  factory _$PendingOutboundMessageCopyWith(_PendingOutboundMessage value,
          $Res Function(_PendingOutboundMessage) _then) =
      __$PendingOutboundMessageCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? contactId,
      String messageId,
      String? messageContent,
      @FileToPathConverter() List<String> filePathsToAttach});
}

/// @nodoc
class __$PendingOutboundMessageCopyWithImpl<$Res>
    implements _$PendingOutboundMessageCopyWith<$Res> {
  __$PendingOutboundMessageCopyWithImpl(this._self, this._then);

  final _PendingOutboundMessage _self;
  final $Res Function(_PendingOutboundMessage) _then;

  /// Create a copy of PendingOutboundMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contactId = freezed,
    Object? messageId = null,
    Object? messageContent = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_PendingOutboundMessage(
      contactId: freezed == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageContent: freezed == messageContent
          ? _self.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _self._filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on
