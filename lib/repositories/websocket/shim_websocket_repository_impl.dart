import 'dart:async';
import 'dart:isolate';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import 'package:web_socket_client/web_socket_client.dart';
import 'package:x1440/api/auth_interceptor.dart';
import 'package:x1440/repositories/models/shim_websocket_message.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/auth/auth_state.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart';
import 'package:x1440/ui/blocs/messaging/messaging_bloc.dart';
import 'package:x1440/ui/blocs/messaging/messaging_event.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/utils/json_utils.dart';

import 'models/websocket_connection.dart';
import 'models/websocket_params.dart';

const Duration _websocketTimeoutOverrideDuration = Duration(seconds: 60);

class ShimWebsocketRepositoryImpl extends ShimWebsocketRepository {
  final RemoteLogger _logger;
  final LocalStorageRepository _localStorage;

  ShimWebsocketRepositoryImpl(this._logger, this._localStorage);

  WebSocket? _socket;

  final StreamController<ShimWebsocketMessage> _messagesStreamController =
      StreamController<ShimWebsocketMessage>.broadcast();
  final StreamController<WebsocketConnection>
      _websocketConnectionStateStreamController =
      StreamController<WebsocketConnection>.broadcast();
  WebsocketConnection _connectionState = const WebsocketConnection();

  @override
  Stream<ShimWebsocketMessage> get messagesStream =>
      _messagesStreamController.stream;

  @override
  Stream<WebsocketConnection> get websocketConnectionStateStream =>
      _websocketConnectionStateStreamController.stream;

  @override
  Future<void> init() async {
    websocketConnectionStateStream.listen(_webSocketConnectionStateListener);
    _setupCredentialsStreamListener();
    _setupAuthStateListenerSafely();
  }

  // Track previous credentials to detect meaningful changes
  String? _previousWebSocketUrl;
  String? _previousAuthorizationToken;
  String? _previousSessionToken;

  void _setupCredentialsStreamListener() {
    _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Setting up credentials stream listener for automatic reconnection');

    // Listen to credentials stream changes - this is the primary mechanism for WebSocket reconnection
    _localStorage.credentialsStream.listen((credentials) async {
      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Credentials changed');
      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: WebSocket URL present: ${credentials.webSocketUrl != null}');
      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Authorization token present: ${credentials.authorizationToken != null}');
      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Session token present: ${credentials.sessionToken != null}');
      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Current connection state: ${_connectionState.connectionState}');

      // Check if this is a meaningful change that requires reconnection
      final webSocketUrlChanged = _previousWebSocketUrl != credentials.webSocketUrl;
      final authTokenChanged = _previousAuthorizationToken != credentials.authorizationToken;
      final sessionTokenChanged = _previousSessionToken != credentials.sessionToken;

      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: WebSocket URL changed: $webSocketUrlChanged');
      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Authorization token changed: $authTokenChanged');
      _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Session token changed: $sessionTokenChanged');

      // Update tracked credentials
      _previousWebSocketUrl = credentials.webSocketUrl;
      _previousAuthorizationToken = credentials.authorizationToken;
      _previousSessionToken = credentials.sessionToken;

      if (credentials.webSocketUrl != null &&
          credentials.authorizationToken != null &&
          credentials.sessionToken != null) {

        // Only reconnect if there's a meaningful change AND we're not already connected
        // Session token changes alone don't require WebSocket reconnection if already connected
        final needsReconnection = webSocketUrlChanged || authTokenChanged ||
                                 _connectionState.connectionState == WebsocketConnectionState.disconnected;

        if (needsReconnection) {
          _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Meaningful credential change detected, forcing reconnection');

          // Wait a moment for credentials to be fully stored
          Future.delayed(const Duration(milliseconds: 500), () async {
            _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Attempting automatic reconnection with new credentials');

            // Disconnect first if not already disconnected
            if (_connectionState.connectionState != WebsocketConnectionState.disconnected) {
              _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Disconnecting from current state: ${_connectionState.connectionState}');
              await disconnect();
              await Future.delayed(const Duration(milliseconds: 500)); // Brief delay after disconnect
            }

            final success = await connect();
            _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Automatic reconnection result: $success');
          });
        } else {
          _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Only session token changed and WebSocket is connected - no reconnection needed');
        }
      } else {
        _logger.info('🔄 WEBSOCKET_CREDENTIALS_LISTENER: Incomplete credentials, disconnecting if connected');
        if (_connectionState.connectionState != WebsocketConnectionState.disconnected) {
          await disconnect();
        }
      }
    });
  }

  void _setupAuthStateListenerSafely() {
    _logger.info('🔄 WEBSOCKET_AUTH_LISTENER: Setting up auth state listener for automatic reconnection');

    // Try to set up the auth state listener, but handle the case where AuthBloc isn't available yet
    try {
      if (GetIt.I.isRegistered<AuthBloc>()) {
        _setupAuthStateListener();
      } else {
        _logger.info('🔄 WEBSOCKET_AUTH_LISTENER: AuthBloc not yet registered, will set up listener later');
        // Schedule a retry after a delay
        Future.delayed(const Duration(seconds: 2), () {
          _setupAuthStateListenerSafely();
        });
      }
    } catch (e) {
      _logger.warn('🔄 WEBSOCKET_AUTH_LISTENER: Failed to set up auth state listener: $e');
      // Schedule a retry after a delay
      Future.delayed(const Duration(seconds: 2), () {
        _setupAuthStateListenerSafely();
      });
    }
  }

  void _setupAuthStateListener() {
    _logger.info('🔄 WEBSOCKET_AUTH_LISTENER: AuthBloc is available, setting up listener');

    // Listen to AuthBloc state changes
    GetIt.I<AuthBloc>().stream.listen((authState) {
      _logger.info('🔄 WEBSOCKET_AUTH_LISTENER: Auth state changed: ${authState.runtimeType}');

      // Check if we just completed a successful login
      if (authState.status == AuthStatus.loggedIn &&
          _connectionState.connectionState == WebsocketConnectionState.disconnected) {
        _logger.info('🔄 WEBSOCKET_AUTH_LISTENER: Detected successful login while disconnected, attempting reconnection');

        // Wait a moment for credentials to be fully stored
        Future.delayed(const Duration(milliseconds: 1000), () async {
          _logger.info('🔄 WEBSOCKET_AUTH_LISTENER: Attempting automatic reconnection after login');
          final success = await connect();
          _logger.info('🔄 WEBSOCKET_AUTH_LISTENER: Automatic reconnection result: $success');
        });
      }
    });
  }

  void _scheduleReconnectionRetries() {
    _logger.info('🔄 WEBSOCKET_401_DETECTED: Scheduling reconnection retries');

    // Schedule multiple retry attempts with increasing delays
    final delays = [3, 6, 10, 15]; // seconds

    for (int i = 0; i < delays.length; i++) {
      final delay = delays[i];
      Future.delayed(Duration(seconds: delay), () async {
        _logger.info('🔄 WEBSOCKET_401_DETECTED: Retry attempt ${i + 1}/${delays.length} after ${delay}s delay');
        _logger.info('🔄 WEBSOCKET_401_DETECTED: Current connection state: ${_connectionState.connectionState}');
        _logger.info('🔄 WEBSOCKET_401_DETECTED: Is disconnected? ${_connectionState.connectionState == WebsocketConnectionState.disconnected}');

        // Check if we're still disconnected or stuck in reconnecting state
        if (_connectionState.connectionState == WebsocketConnectionState.disconnected ||
            _connectionState.connectionState == WebsocketConnectionState.reconnecting) {
          _logger.info('🔄 WEBSOCKET_401_DETECTED: Connection state is ${_connectionState.connectionState}, attempting reconnection');

          // Force disconnect first if we're stuck in reconnecting state
          if (_connectionState.connectionState == WebsocketConnectionState.reconnecting) {
            _logger.info('🔄 WEBSOCKET_401_DETECTED: Forcing disconnect from stuck reconnecting state');
            await disconnect();
            await Future.delayed(const Duration(milliseconds: 500)); // Brief delay
          }

          final success = await connect();
          _logger.info('🔄 WEBSOCKET_401_DETECTED: Retry ${i + 1} result: $success');

          // If successful, stop further retries
          if (success) {
            _logger.info('🔄 WEBSOCKET_401_DETECTED: Reconnection successful, stopping further retries');
            return;
          }
        } else {
          _logger.info('🔄 WEBSOCKET_401_DETECTED: Connection state is ${_connectionState.connectionState}, skipping retry ${i + 1}');
        }
      });
    }
  }

  void _scheduleDirectReconnectionAfterAutoLogin() {
    _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: Scheduling direct reconnection after auto-login');

    // Schedule a direct reconnection attempt after auto-login has time to complete
    // This bypasses the credentials stream listener which may not be working
    Future.delayed(const Duration(seconds: 8), () async {
      _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: Attempting direct reconnection after auto-login delay');
      _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: Current connection state: ${_connectionState.connectionState}');

      if (_connectionState.connectionState == WebsocketConnectionState.disconnected ||
          _connectionState.connectionState == WebsocketConnectionState.reconnecting) {
        _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: Connection state is ${_connectionState.connectionState}, forcing reconnection');

        // Force disconnect first if we're stuck in reconnecting state
        if (_connectionState.connectionState == WebsocketConnectionState.reconnecting) {
          _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: Forcing disconnect from stuck reconnecting state');
          await disconnect();
          await Future.delayed(const Duration(milliseconds: 500)); // Brief delay
        }

        final success = await connect();
        _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: Direct reconnection result: $success');

        if (success) {
          _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: ✅ WebSocket successfully reconnected after auto-login!');
        } else {
          _logger.warn('🔄 WEBSOCKET_DIRECT_RECONNECT: ❌ Direct reconnection failed, WebSocket remains disconnected');
        }
      } else {
        _logger.info('🔄 WEBSOCKET_DIRECT_RECONNECT: Already connected (${_connectionState.connectionState}), skipping direct reconnection');
      }
    });
  }

  @override
  Future<bool> connect() async {
    // HYPOTHESIS 3: Add logging for websocket connection attempts
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: connect() called');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Current connection state: ${_connectionState.connectionState}');
    final result = await _connectToWebsocket();
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: _connectToWebsocket() returned: $result');
    return result;
  }

  @override
  Future<void> disconnect() async {
    _logger.info('disconnecting from websocket');
    _socket?.close();
    _socket = null;
  }

  /// Force refresh credentials and retry connection - useful for debugging auth issues
  Future<bool> connectWithFreshCredentials() async {
    _logger.info('🔄 WEBSOCKET_CONNECT_DEBUG: connectWithFreshCredentials() called - forcing credential refresh');

    // Wait a moment for any ongoing auth operations to complete
    await Future.delayed(const Duration(milliseconds: 500));

    return _connectToWebsocket();
  }

  int connectAttempts = 0;
  Future<bool> _connectToWebsocket() async {
    // HYPOTHESIS 3: Add comprehensive logging for websocket connection process
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: _connectToWebsocket() started');

    WebsocketParams? params = await _getParamsFromLocalStorage();
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Got params from storage: ${params != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Params URI: "${params?.uri}"');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Params headers: ${params?.headers}');

    if (_connectionState.connectionState.isConnected ||
        _connectionState.connectionState.isConnecting) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Already connected/connecting, returning true');
      return true;
    }

    if (params?.uri == null) {
      _logger.warn('🔍 WEBSOCKET_CONNECT_DEBUG: No URI available, cannot connect');
      return false;
    }

    _logger.info(
        'connectToWebsocket attempt: ${connectAttempts++} with connectionState: ${_connectionState.connectionState}; X-Correlation-Id: ${params?.headers?['X-Correlation-Id']}');

    Isolate currentIsolate = Isolate.current;

    params = params?.copyWith(
      headers: {
        ...?params.headers,
        'X-Connection-Id': const Uuid().v4(),
        'X-Isolate-Name': currentIsolate.debugName,
      },
    );

    setupWebsocketTimeoutOverride();

    _logger.info('closing existing socket and connecting with: ${params?.uri}');
    _socket?.close();
    _socket = null;
    _socket = _connectToWebSocketFromParams(params!);
    _socket?.connection.listen((event) {
      try {
      _logger.info('socket connection event: ${event.runtimeType}');
      _connectionState = WebsocketConnection.fromConnection(event);
      _logger.info('socket connection state: ${_connectionState.connectionState}');
      _websocketConnectionStateStreamController.add(_connectionState);

      if (_connectionState.connectionState.isConnected == true) {
        _websocketTimeoutOverride?.cancel();
        _hasRetriedAfterTimeout = false;
      }

      if (event is Disconnected) {
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: DISCONNECTED EVENT HANDLER CALLED!');
        _logger.info(
            'socket disconnected reason: ${event.reason}; error: ${event
                .error}; code: ${event.code}');

        // COMPREHENSIVE ERROR ANALYSIS: Log everything about the error
        final errorString = event.error?.toString() ?? 'null';
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: Error string: "$errorString"');
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: Contains "was not upgraded to websocket": ${errorString.contains('was not upgraded to websocket')}');
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: Contains "HTTP status code: 401": ${errorString.contains('HTTP status code: 401')}');

        // TODO: can we better match this? It seems to be standard text with a null error code . . . ideally we'd have an error code to match for this?
        if (errorString.contains('was not upgraded to websocket')) {
          _logger.info(
              'socket disconnected due to not being upgraded to websocket');

          // Check if this is a 401 authentication error and trigger auto-login
          if (errorString.contains('HTTP status code: 401') ||
              errorString.contains('status code: 401')) {
            _logger.info('🚨 WEBSOCKET_401_DETECTED: Triggering auto-login for websocket 401 error');
            _logger.info('🚨 WEBSOCKET_401_DETECTED: Current credentials before auto-login attempt');

            // Log current credential state before triggering auto-login
            _localStorage.getCredentials().then((creds) {
              _logger.info('🚨 WEBSOCKET_401_DETECTED: WebSocket URL: ${creds.webSocketUrl != null ? "present" : "missing"}');
              _logger.info('🚨 WEBSOCKET_401_DETECTED: Session token: ${creds.sessionToken != null ? "present" : "missing"}');
              _logger.info('🚨 WEBSOCKET_401_DETECTED: Auth token: ${creds.authorizationToken != null ? "present" : "missing"}');
            });

            // Try to get AuthBloc safely
            try {
              if (GetIt.I.isRegistered<AuthBloc>()) {
                GetIt.I<AuthBloc>().add(AttemptAutoLoginEvent(force: true));
                _logger.info('🔄 WEBSOCKET_401_DETECTED: Auto-login triggered');

                // Schedule multiple retry attempts with increasing delays
                _scheduleReconnectionRetries();

                // Also schedule a direct reconnection attempt after auto-login completes
                _scheduleDirectReconnectionAfterAutoLogin();
              } else {
                _logger.warn('🔄 WEBSOCKET_401_DETECTED: AuthBloc not available, cannot trigger auto-login');
              }
            } catch (e) {
              _logger.error('🔄 WEBSOCKET_401_DETECTED: Error triggering auto-login: $e');
            }
          } else {
            _logger.info('⚠️ WEBSOCKET_NON_401: Non-401 websocket upgrade failure');
          }
        } else if (event.code != null) {
          _logger.info(
              'socket disconnected with code: ${event
                  .code}; attempting to reconnect');
          _handleWebsocketDisconnect();
        } else {
          // HYPOTHESIS 1: Log all other error types
          _logger.info('🔍 WEBSOCKET_DEBUG: Non-upgrade error: "${event.error.toString()}"');
        }
      }
    } catch (e) {
      _logger.warn('socket connection error: $e');
    }
    }, onError: (error) {
      _logger.warn('socket error: $error');
      // _websocketConnectionStateStream.add(WebsocketConnection.fromError(error));
    }, onDone: () {
      _logger.info('socket done');
      _socket?.close();
      _socket = null;
      // _websocketConnectionStateStream.add(WebsocketConnection.fromDone());
    });

    _socket?.messages.listen((event) {
      Map<String, dynamic> eventJson = (event is Map<String, dynamic>)
          ? event
          : () {
              try {
                return safeJsonDecode(event);
              } catch (e) {
                _logger.warn('error parsing message: $e');
                return {
                  'message': 'error parsing message',
                  'error': e.toString(),
                  'raw': event
                };
              }
            }();

      _messagesStreamController.add(ShimWebsocketMessage.fromJson(eventJson));
    });

    _logger.info('finishing connecting with: ${_socket?.connection.state}');
    final isConnected = _connectionState.connectionState.isConnected == true;
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: _connectToWebsocket() returned: $isConnected');
    return isConnected;
  }

  Future<WebsocketParams?> _getParamsFromLocalStorage() async {
    final credentials = await _localStorage.getCredentials();

    String? websocketUrl = credentials.webSocketUrl;

    // ENHANCED LOGGING: Log all credential states for debugging
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Getting credentials from storage');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: WebSocket URL present: ${websocketUrl != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Session token present: ${credentials.sessionToken != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Authorization token present: ${credentials.authorizationToken != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Access token present: ${credentials.accessToken != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: User ID present: ${credentials.userId != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Instance URL present: ${credentials.instanceUrl != null}');

    if (websocketUrl != null) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: WebSocket URL: "$websocketUrl"');
    }

    if (credentials.sessionToken != null) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Session token (first 20 chars): "${credentials.sessionToken!.substring(0, credentials.sessionToken!.length > 20 ? 20 : credentials.sessionToken!.length)}..."');
    }

    if (credentials.authorizationToken != null) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Authorization token (first 20 chars): "${credentials.authorizationToken!.substring(0, credentials.authorizationToken!.length > 20 ? 20 : credentials.authorizationToken!.length)}..."');
    }

    if (websocketUrl == null ||
        credentials.sessionToken == null ||
        credentials.authorizationToken == null) {
      _logger.warn('🚨 WEBSOCKET_CONNECT_DEBUG: Missing required credentials for WebSocket connection');
      return null;
    }

    Map<String, dynamic> headers = {
      "Authorization": "Bearer ${credentials.authorizationToken}",
      sessionTokenHeaderKey: credentials.sessionToken,
      "X-Correlation-Id": const Uuid().v4(),
    };

// Initially wait 1s and double the wait time until a maximum step of of 3 is reached.
// [1, 2, 4, 16, 32, 64]
    final backoff = BinaryExponentialBackoff(
        initial: const Duration(seconds: 1), maximumStep: 6);

    return WebsocketParams(
        uri: Uri.parse(websocketUrl),
        headers: headers,
        backoff: backoff,
        pingInterval: const Duration(seconds: 5));
  }

  bool _hasRetriedAfterTimeout = false;
  Timer? _websocketTimeoutOverride;
  void setupWebsocketTimeoutOverride() {
    _websocketTimeoutOverride?.cancel();
    _websocketTimeoutOverride =
        Timer(_websocketTimeoutOverrideDuration, () async {
      _logger.warn(
          'websocket connection HARD timed out, current connection state: $_connectionState');
      if (_connectionState.connectionState.isConnected == true) {
        return;
      } else if (_connectionState.connectionState !=
          WebsocketConnectionState.disconnected) {
        _logger.warn('disconnecting from websocket due to timeout');
        await disconnect();
      }
      if (!_hasRetriedAfterTimeout) {
        _hasRetriedAfterTimeout = true;
        await Future.delayed(const Duration(seconds: 5));
        _logger.warn('reconnecting to MessagingBloc after timeout');
        GetIt.I<MessagingBloc>().add(ConnectEvent());
      } else {
        _logger.warn('websocket timeout - checking auth state before triggering auto-login');

        // Check if user is already logged in before triggering auto-login
        try {
          if (GetIt.I.isRegistered<AuthBloc>()) {
            final authBloc = GetIt.I<AuthBloc>();
            final currentState = authBloc.state;

            if (currentState.status == AuthStatus.loggedIn) {
              _logger.info('🔄 WEBSOCKET_TIMEOUT: User is already logged in, attempting simple reconnection instead of auto-login');
              GetIt.I<MessagingBloc>().add(ConnectEvent());
            } else {
              _logger.warn('🔄 WEBSOCKET_TIMEOUT: User not logged in (${currentState.status}), triggering auto-login');
              authBloc.add(AttemptAutoLoginEvent(force: true));
            }
          } else {
            _logger.warn('🔄 WEBSOCKET_TIMEOUT: AuthBloc not available, cannot check auth state');
            // Fallback to simple reconnection instead of auto-login
            GetIt.I<MessagingBloc>().add(ConnectEvent());
          }
        } catch (e) {
          _logger.error('🔄 WEBSOCKET_TIMEOUT: Error checking auth state: $e');
          // Fallback to simple reconnection instead of auto-login
          GetIt.I<MessagingBloc>().add(ConnectEvent());
        }
      }
    });
  }

  WebSocket _connectToWebSocketFromParams(WebsocketParams params) =>
      WebSocket(params.uri!,
          headers: params.headers,
          protocols: params.protocols,
          pingInterval: params.pingInterval,
          backoff: params.backoff,
          timeout: params.timeout,
          binaryType: params.binaryType);

  Future<void> _handleWebsocketDisconnect() async {
    _logger.info('handleWebsocketDisconnect');
    _socket?.close();
    _socket = null;
  }

  void _webSocketConnectionStateListener(WebsocketConnection connection) {
    _logger.info('setWebSocketConnectionState: $connection');
    _connectionState = connection;
  }

  /// Debug method to test WebSocket connection with current credentials
  Future<void> debugTestConnection() async {
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Starting manual connection test');

    final credentials = await _localStorage.getCredentials();
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Current credentials state:');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - WebSocket URL: ${credentials.webSocketUrl}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Session token present: ${credentials.sessionToken != null}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Auth token present: ${credentials.authorizationToken != null}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Access token present: ${credentials.accessToken != null}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - User ID: ${credentials.userId}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Instance URL: ${credentials.instanceUrl}');

    if (credentials.webSocketUrl != null &&
        credentials.sessionToken != null &&
        credentials.authorizationToken != null) {
      _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Credentials look complete, attempting connection');
      final result = await connect();
      _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Connection result: $result');
    } else {
      _logger.warn('🧪 WEBSOCKET_DEBUG_TEST: Incomplete credentials, cannot test connection');
    }
  }
}
