import 'dart:convert';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:pointycastle/export.dart' as pc;

class EncryptionService {
  final _keyLength = 32;
  final _iterations = 1000;

  EncryptionService();

  String encryptData(String data, String password, int timestamp, String salt) {
    final encrypter = _createEncrypter(data, password, timestamp, salt);
    return encrypter.encrypt(data).base64;
  }

  String decryptData(String data, String password, int timestamp, String salt) {
    final encrypter = _createEncrypter(data, password, timestamp, salt);
    final encrypted = Encrypted.fromBase64(data);
    return encrypter.decrypt(encrypted);
  }

  String sign(String method, String path, int timestamp, String password,
      String? body) {
    String signingString = "$method:$path:$timestamp";
    if (body != null && body.isNotEmpty) {
      var h = sha512.convert(utf8.encode(body)).toString();
      signingString += ":$h";
    }
    String pwd = "$password-$timestamp-1440";
    var sig = Hmac(sha256, utf8.encode(pwd))
        .convert(utf8.encode(signingString))
        .toString();

    return sig;
  }

  List<int> _deriveKey(
      String password, List<int> salt, int iterations, int keyLength) {
    var pbkdf2 = pc.PBKDF2KeyDerivator(pc.HMac(pc.SHA256Digest(), 64))
      ..init(
          pc.Pbkdf2Parameters(Uint8List.fromList(salt), iterations, keyLength));
    return pbkdf2.process(utf8.encode(password));
  }

  Encrypter _createEncrypter(
      String data, String password, int timestamp, String salt) {
    final String stringKey = "1440-$password-$timestamp";
    final String hashed = sha256.convert(utf8.encode(salt)).toString();
    final newSalt = utf8.encode(hashed.substring(0, 16));

    final key = Key(Uint8List.fromList(
        _deriveKey(stringKey, newSalt, _iterations, _keyLength)));
    return Encrypter(Fernet(key));
  }
}
