import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/isolate/network_service_factory.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/frameworks/presence/presence_event.dart';
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';
import 'package:x1440/utils/loggy_utils.dart';
import 'package:x1440/utils/performance_monitor.dart';
import 'package:x1440/utils/Utils.dart';

import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc(super.initialState);
}

class AuthBlocImpl extends AuthBloc with BlocLoggy {
  final AuthUseCase _authUseCase;
  final SessionUseCase _sessionUseCase;
  final ConversationsUseCase _conversationsUseCase;
  final MessagingUseCase _messagingUseCase;
  final AppLifeCycleRepository _appLifeCycleRepository;
  final RemoteLogger _logger;

  AuthBlocImpl(
      this._authUseCase,
      this._sessionUseCase,
      this._conversationsUseCase,
      this._messagingUseCase,
      this._appLifeCycleRepository,
      this._logger
      )
      : super(const AuthState()) {
    on<AttemptAutoLoginEvent>(_onAttemptAutoLoginEvent);
    on<LogoutEvent>(_onLogoutEvent);
    on<SessionExpiredEvent>(_onSessionExpiredEvent);
    on<KeepSessionAliveEvent>(_onKeepSessionAliveEvent);
    on<SalesforceOAuthEvent>(_onSalesforceOAuthEvent);
    on<SalesforceOAuthErrorEvent>(_onSalesforceOAuthErrorEvent);
    on<SalesforceOAuthResponseEvent>(_onSalesforceOAuthResponseEvent);
    on<InitializeAuthStateEvent>(_onInitializeAuthStateEvent);

    // Initialize auth state when the bloc is created
    _initializeAuthState();
  }

  /// Initialize auth state by checking existing session
  void _initializeAuthState() {
    // Use a small delay to ensure the bloc is fully initialized
    Future.delayed(const Duration(milliseconds: 100), () {
      add(InitializeAuthStateEvent());
    });
  }

  Future<void> _onInitializeAuthStateEvent(
      InitializeAuthStateEvent event, Emitter<AuthState> emit) async {
    loggy.info('⏱️ AUTH TIMING - _onInitializeAuthStateEvent START');

    try {
      // Check if user is already logged in
      final isLoggedIn = await _authUseCase.isLoggedIn;
      loggy.info('🔍 AUTH_INIT: Checking existing session - isLoggedIn: $isLoggedIn');

      if (isLoggedIn) {
        loggy.info('✅ AUTH_INIT: User is already logged in, updating auth state');
        emit(state.copyWith(status: AuthStatus.loggedIn));

        // Resume existing session and ensure proper initialization
        loggy.info('🔄 AUTH_INIT: Resuming existing session and ensuring proper initialization');
        try {
          // First, ensure network isolate has the correct tokens
          final credentials = await _authUseCase.getCredentials();
          if (credentials.sessionToken != null && credentials.authorizationToken != null) {
            loggy.info('🔄 AUTH_INIT: Updating network isolate with existing session tokens');
            await GetIt.I<NetworkServiceFactory>().updateAuthTokens(
              authorizationToken: credentials.authorizationToken,
              accessToken: credentials.sessionToken,
            );

            // Add a small delay to ensure the network isolate has processed the token update
            await Future.delayed(const Duration(milliseconds: 100));
            loggy.info('✅ AUTH_INIT: Network isolate updated with existing session tokens');
          }

          // Initialize conversations bloc
          if (GetIt.I.isRegistered<ConversationsBloc>()) {
            GetIt.I<ConversationsBloc>().add(InitEvent());

            // For existing sessions, always try to fetch conversations
            // The ConversationsBloc will handle the case where SCRT credentials are not available
            loggy.info('🔄 AUTH_INIT: Fetching conversations for existing session');
            GetIt.I<ConversationsBloc>().add(FetchConversationsEvent());
          }
        } catch (e) {
          loggy.error('❌ AUTH_INIT: Error resuming session: $e');
        }

        // Also trigger presence status initialization for existing session
        loggy.info('🔄 AUTH_INIT: Triggering presence status initialization for existing session');
        try {
          if (GetIt.I.isRegistered<PresenceManager>()) {
            // Use the presence manager to handle presence initialization
            final presenceManager = GetIt.I<PresenceManager>();
            presenceManager.add(InitializePresenceForExistingSessionEvent());
          }
        } catch (e) {
          loggy.error('❌ AUTH_INIT: Error triggering presence status initialization: $e');
        }

        // Ensure WebSocket connection is established for existing sessions
        loggy.info('🔄 AUTH_INIT: Establishing WebSocket connection for existing session');
        try {
          if (GetIt.I.isRegistered<MessagingUseCase>()) {
            // Connect to WebSocket to receive conversation status updates
            GetIt.I<MessagingUseCase>().connectToWebsocket();
            loggy.info('✅ AUTH_INIT: WebSocket connection initiated for existing session');
          }
        } catch (e) {
          loggy.error('❌ AUTH_INIT: Error establishing WebSocket connection: $e');
        }
      } else {
        loggy.info('ℹ️ AUTH_INIT: User is not logged in, keeping loggedOut state');
        emit(state.copyWith(status: AuthStatus.loggedOut));
      }
    } catch (e) {
      loggy.error('❌ AUTH_INIT: Error checking auth state: $e');
      emit(state.copyWith(status: AuthStatus.loggedOut));
    }

    loggy.info('⏱️ AUTH TIMING - _onInitializeAuthStateEvent COMPLETE');
  }

  Future<void> _onAttemptAutoLoginEvent(
      AttemptAutoLoginEvent event, Emitter<AuthState> emit) async {
    final perfMon = PerformanceMonitor.instance;
    perfMon.startMeasure('auth_attempt_auto_login_total');
    loggy.info('⏱️ AUTH TIMING - _onAttemptAutoLoginEvent START');
    
    if (kDebugMode) {
      print(
          'AUTHBLOC-attempting auto login with: ${state.status}; isForegrounded: ${_appLifeCycleRepository.isForegrounded}; state: ${_appLifeCycleRepository.appLifecycleState}');
    }

    // Check if we should attempt login
    perfMon.startMeasure('auth_check_should_attempt');
    final bool shouldAttempt = _appLifeCycleRepository.isForegrounded &&
        (event.force || state.status == AuthStatus.loggedOut);
    perfMon.endMeasure('auth_check_should_attempt');

    if (!shouldAttempt) {
      loggy.info('⏱️ AUTH TIMING - _onAttemptAutoLoginEvent SKIPPED - shouldAttempt: $shouldAttempt, isForegrounded: ${_appLifeCycleRepository.isForegrounded}, status: ${state.status}, force: ${event.force}');
      perfMon.log('Auto login attempt skipped - not foregrounded or not logged out state');
      perfMon.endMeasure('auth_attempt_auto_login_total');
      return;
    }

    // Additional check: if user is already logged in and this isn't a forced attempt, skip
    if (state.status == AuthStatus.loggedIn && !event.force) {
      loggy.info('⏱️ AUTH TIMING - _onAttemptAutoLoginEvent SKIPPED - user already logged in');
      perfMon.log('Auto login attempt skipped - user already logged in');
      perfMon.endMeasure('auth_attempt_auto_login_total');
      return;
    }
    
    // Emit logging in state
    perfMon.startMeasure('auth_emit_logging_in');
    emit(state.copyWith(status: AuthStatus.isLoggingIn));
    perfMon.endMeasure('auth_emit_logging_in');
    
    // Disconnect websocket
    perfMon.startMeasure('auth_disconnect_websocket');
    _messagingUseCase.disconnectWebsocket();
    perfMon.endMeasure('auth_disconnect_websocket');
    
    // Attempt auto login
    perfMon.startMeasure('auth_attempt_auto_login_use_case');
    final autoLoginSuccess = await _authUseCase.attemptAutoLogin();
    final autoLoginDuration = perfMon.endMeasure('auth_attempt_auto_login_use_case');
    perfMon.log('attemptAutoLogin completed in ${autoLoginDuration}ms with result: $autoLoginSuccess');
    
    // Handle login result
    if (autoLoginSuccess) {
      loggy.info('AuthBloc - AttemptAutoLoginEvent - Success');
      perfMon.startMeasure('auth_emit_logged_in');
      emit(state.copyWith(status: AuthStatus.loggedIn));
      perfMon.endMeasure('auth_emit_logged_in');

      // Reconnect websocket after successful authentication and session establishment
      perfMon.startMeasure('auth_reconnect_websocket');
      // The websocket will automatically reconnect via MessagingService.credentialsStream
      // when the session is established and credentials are updated with webSocketUrl
      perfMon.endMeasure('auth_reconnect_websocket');
    } else {
      loggy.info('AuthBloc - AttemptAutoLoginEvent - Failed');

      // Check if we're already logged in with a valid session before triggering logout
      final currentlyLoggedIn = await _authUseCase.isLoggedIn;
      if (currentlyLoggedIn && state.status == AuthStatus.loggedIn) {
        loggy.info('⚠️ AUTH - Auto-login failed but user is already logged in with valid session, skipping logout');
        perfMon.startMeasure('auth_emit_logged_in_after_failed_auto_login');
        emit(state.copyWith(status: AuthStatus.loggedIn));
        perfMon.endMeasure('auth_emit_logged_in_after_failed_auto_login');
      } else {
        // Auto-login failed - but don't immediately assume session expired
        // Only show "session expired" for specific token-related failures
        loggy.info('ℹ️ AUTH - Auto-login failed, checking if this is a session expiration');

        // For now, don't show "session expired" message automatically
        // Let the user try to log in normally, and if there's a real session conflict,
        // it will be handled by the session conflict resolution logic
        loggy.info('ℹ️ AUTH - Auto-login failed, user can attempt manual login');

        perfMon.startMeasure('auth_emit_logged_out');
        emit(state.copyWith(status: AuthStatus.loggedOut));
        perfMon.endMeasure('auth_emit_logged_out');
      }
    }
    
    final totalDuration = perfMon.endMeasure('auth_attempt_auto_login_total');
    loggy.info('⏱️ AUTH TIMING - _onAttemptAutoLoginEvent COMPLETE (${totalDuration}ms)');
  }

  FutureOr<void> _onSalesforceOAuthEvent(
      SalesforceOAuthEvent event, Emitter<AuthState> emit) async {
    final perfMon = PerformanceMonitor.instance;
    perfMon.startMeasure('auth_salesforce_oauth_total');
    
    perfMon.startMeasure('auth_emit_logging_in');
    emit(state.copyWith(status: AuthStatus.isLoggingIn));
    perfMon.endMeasure('auth_emit_logging_in');
    
    perfMon.startMeasure('auth_login_to_salesforce');
    final result = await _authUseCase.loginToSalesforce();
    perfMon.endMeasure('auth_login_to_salesforce');

    loggy.info('AuthBloc - LoginEvent - Success: ${(result)}');
    perfMon.log('Login result type: ${result.runtimeType}');
    
    if (result is Success) {
      perfMon.startMeasure('auth_handle_oauth_success');
      final success = result as Success;
      add(SalesforceOAuthResponseEvent(response: success.data));
      perfMon.endMeasure('auth_handle_oauth_success');
    } else {
      perfMon.startMeasure('auth_handle_oauth_error');
      final error = result as Error;
      add(SalesforceOAuthErrorEvent(error: error.error.toString()));
      perfMon.endMeasure('auth_handle_oauth_error');
    }
    
    perfMon.endMeasure('auth_salesforce_oauth_total');
  }

  FutureOr<void> _onSalesforceOAuthErrorEvent(
      SalesforceOAuthErrorEvent event, Emitter<AuthState> emit) {
    final perfMon = PerformanceMonitor.instance;
    perfMon.startMeasure('auth_handle_oauth_error_event');
    
    loggy.info('SalesforceOAuthErrorEvent - Error: ${event.error}');
    perfMon.log('OAuth Error: ${event.error}');

    emit(state.copyWith(
        status: AuthStatus.loggedOut, logOut: UiEvent(Nothing())));
    
    perfMon.endMeasure('auth_handle_oauth_error_event');
  }

  FutureOr<void> _onSalesforceOAuthResponseEvent(
      SalesforceOAuthResponseEvent event, Emitter<AuthState> emit) async {
    final perfMon = PerformanceMonitor.instance;
    perfMon.startMeasure('auth_oauth_response_total');

    loggy.info('SalesforceOAuthResponseEvent - Response: ${event.response}');

    try {
      perfMon.startMeasure('auth_handle_oauth_response');
      await _authUseCase.handleOAuthResponse(event.response);
      perfMon.endMeasure('auth_handle_oauth_response');

      perfMon.startMeasure('auth_shim_login_and_start_session');
      await _authUseCase.shimLoginAndStartSession();
      perfMon.endMeasure('auth_shim_login_and_start_session');

      perfMon.startMeasure('auth_check_is_logged_in');
      final isLoggedIn = await _authUseCase.isLoggedIn;
      perfMon.endMeasure('auth_check_is_logged_in');

      if (isLoggedIn) {
        perfMon.startMeasure('auth_emit_logged_in_with_event');
        emit(state.copyWith(
            status: AuthStatus.loggedIn, logIn: UiEvent(Nothing())));
        perfMon.endMeasure('auth_emit_logged_in_with_event');
      } else {
        loggy.error('❌ AUTH - OAuth login completed but user is not logged in');
        emit(state.copyWith(
            status: AuthStatus.loggedOut, logOut: UiEvent(Nothing())));
      }
    } catch (e) {
      loggy.error('❌ AUTH - OAuth login failed: $e');

      // Check if this is a session conflict error
      if (e.toString().contains('UserAlreadyLoggedIn') ||
          e.toString().contains('active session') ||
          e.toString().contains('Session conflict')) {
        loggy.info('🔄 AUTH - Session conflict detected during OAuth login, will be resolved automatically');

        // Show user-friendly message for session conflict
        try {
          Utils.showToast(
            'Resolving existing session conflict. Please wait...',
            type: ToastType.info,
          );
        } catch (toastError) {
          loggy.info('⚠️ AUTH - Error showing session conflict toast: $toastError');
        }

        // The session conflict resolution will happen automatically in the network isolate
        // Just emit logged out state and let the user try again
        emit(state.copyWith(
            status: AuthStatus.loggedOut, logOut: UiEvent(Nothing())));
      } else {
        // Other errors - show generic error
        try {
          Utils.showToast(
            'Login failed. Please try again.',
            type: ToastType.error,
          );
        } catch (toastError) {
          loggy.info('⚠️ AUTH - Error showing login error toast: $toastError');
        }

        emit(state.copyWith(
            status: AuthStatus.loggedOut, logOut: UiEvent(Nothing())));
      }
    } finally {
      // Reconnect websocket after successful OAuth authentication and session establishment
      perfMon.startMeasure('auth_reconnect_websocket_oauth');
      // The websocket will automatically reconnect via MessagingService.credentialsStream
      // when the session is established and credentials are updated with webSocketUrl
      perfMon.endMeasure('auth_reconnect_websocket_oauth');
    }
    
    perfMon.endMeasure('auth_oauth_response_total');
  }

  Future<void> _onLogoutEvent(
      LogoutEvent event, Emitter<AuthState> emit) async {
    final perfMon = PerformanceMonitor.instance;
    perfMon.startMeasure('auth_logout_total');
    
    loggy.info('Logout Event Received');
    
    perfMon.startMeasure('auth_emit_logging_out');
    emit(state.copyWith(status: AuthStatus.isLoggingOut));
    perfMon.endMeasure('auth_emit_logging_out');
    
    perfMon.startMeasure('auth_set_is_logging_out');
    _authUseCase.setIsLoggingOut();
    perfMon.endMeasure('auth_set_is_logging_out');
    
    perfMon.startMeasure('auth_handle_presence_offline');
    await GetIt.I<ConversationsService>().handlePresenceOffline();
    perfMon.endMeasure('auth_handle_presence_offline');

    /// make sure we're not on a page that relies on any view models that are getting disposed in the logout
    try {
      // End session and logout
      perfMon.startMeasure('auth_end_session');
      await _sessionUseCase.endSession().timeout(const Duration(seconds: 20));
      perfMon.endMeasure('auth_end_session');
      
      perfMon.startMeasure('auth_logout');
      await _authUseCase.logout().timeout(const Duration(seconds: 20));
      perfMon.endMeasure('auth_logout');

      perfMon.startMeasure('auth_clear_conversations');
      _conversationsUseCase.clear();
      perfMon.endMeasure('auth_clear_conversations');
      // TODO: better handle state resetting ... GetIt Scope?
      // GetIt.I<ConversationsBloc>().add(ClearConversationsEvent());
    } catch (e) {
      perfMon.log('Error logging out: $e');
      _logger.error('Error logging out: $e');
    }
    
    perfMon.startMeasure('auth_set_is_logging_out_false');
    _authUseCase.setIsLoggingOut(false);
    perfMon.endMeasure('auth_set_is_logging_out_false');
    
    perfMon.startMeasure('auth_emit_logged_out');
    emit(state.copyWith(
        status: AuthStatus.loggedOut,
        appError: event.appError,
        logOut: UiEvent(Nothing())));
    perfMon.endMeasure('auth_emit_logged_out');
    
    perfMon.endMeasure('auth_logout_total');
  }

  Future<void> _onSessionExpiredEvent(
      SessionExpiredEvent event, Emitter<AuthState> emit) async {
    final perfMon = PerformanceMonitor.instance;
    perfMon.startMeasure('auth_session_expired_event');
    
    perfMon.startMeasure('auth_check_is_logged_in');
    final bool isLoggedIn = await _authUseCase.isLoggedIn;
    perfMon.endMeasure('auth_check_is_logged_in');
    
    perfMon.log('Session expired - isLoggedIn: $isLoggedIn, isLoggingOut: ${_authUseCase.isLoggingOut}');
    loggy.info(
        'Session Expired Event Received with isLoggedIn: $isLoggedIn; isLoggingOut: ${_authUseCase.isLoggingOut}');
    
    if (isLoggedIn && !_authUseCase.isLoggingOut) {
      loggy.info('_onSessionExpiredEvent - logging out');
      perfMon.startMeasure('auth_add_logout_event_from_session_expired');
      add(LogoutEvent());
      perfMon.endMeasure('auth_add_logout_event_from_session_expired');
    }
    
    perfMon.endMeasure('auth_session_expired_event');
  }

  Future<void> _onKeepSessionAliveEvent(
      KeepSessionAliveEvent event, Emitter<AuthState> emit) async {
    final perfMon = PerformanceMonitor.instance;
    perfMon.startMeasure('auth_keep_session_alive_total');

    perfMon.startMeasure('auth_keep_session_alive');
    final success = await _sessionUseCase.keepSessionAlive();
    perfMon.endMeasure('auth_keep_session_alive');
    perfMon.log('Keep session alive result: $success');

    if (!success) {
      loggy.info('KeepSessionAliveEvent failed - triggering auto-login');

      // Show warning toast for keep-alive failure
      try {
        Utils.showToast(
          'Session refresh failed. Attempting to reconnect...',
          type: ToastType.warning,
        );
        loggy.info('✅ AuthBloc - Displayed warning toast for keep-alive failure');
      } catch (toastError) {
        loggy.info('⚠️ AuthBloc - Error showing toast: $toastError');
      }

      perfMon.startMeasure('auth_add_attempt_auto_login');
      add(AttemptAutoLoginEvent());
      perfMon.endMeasure('auth_add_attempt_auto_login');
      perfMon.endMeasure('auth_keep_session_alive_total');
      return;
    }

    perfMon.startMeasure('auth_set_is_logging_out_false');
    _authUseCase.setIsLoggingOut(false);
    perfMon.endMeasure('auth_set_is_logging_out_false');

    perfMon.startMeasure('auth_emit_logged_in');
    emit(state.copyWith(status: AuthStatus.loggedIn));
    perfMon.endMeasure('auth_emit_logged_in');

    perfMon.endMeasure('auth_keep_session_alive_total');
  }
}
