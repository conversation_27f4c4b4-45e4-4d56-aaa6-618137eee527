import 'package:x1440/api/salesforce/dtos/oauth_response.dart';
import 'package:x1440/models/app_error_model.dart';

abstract class AuthEvent {}

class AuthStartedEvent extends AuthEvent {}

class LoginEvent extends AuthEvent {
  final String language;

  LoginEvent({required this.language});
}

class LogoutEvent extends AuthEvent {
  final AppError? appError;

  LogoutEvent({this.appError});
}

class AttemptAutoLoginEvent extends AuthEvent {
  final bool force;
  AttemptAutoLoginEvent({this.force = false});
}

class InitializeAuthStateEvent extends AuthEvent {}

class SessionExpiredEvent extends AuthEvent {}

class KeepSessionAliveEvent extends AuthEvent {}

class SalesforceOAuthEvent extends AuthEvent {}

class SalesforceOAuthResponseEvent extends AuthEvent {
  final OAuthResponse response;

  SalesforceOAuthResponseEvent({required this.response});
}

class SalesforceOAuthErrorEvent extends AuthEvent {
  final String error;

  SalesforceOAuthErrorEvent({required this.error});
}
