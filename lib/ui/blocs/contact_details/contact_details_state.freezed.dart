// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContactDetailsState {
  @JsonKey(ignore: true)
  ContactOrMeuAllDetails? get contactDetails;
  Contact? get contact;
  String? get conversationUserName;
  MessagingEndUser? get messagingEndUser;
  bool get isLoading;

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactDetailsStateCopyWith<ContactDetailsState> get copyWith =>
      _$ContactDetailsStateCopyWithImpl<ContactDetailsState>(
          this as ContactDetailsState, _$identity);

  /// Serializes this ContactDetailsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactDetailsState &&
            (identical(other.contactDetails, contactDetails) ||
                other.contactDetails == contactDetails) &&
            (identical(other.contact, contact) || other.contact == contact) &&
            (identical(other.conversationUserName, conversationUserName) ||
                other.conversationUserName == conversationUserName) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactDetails, contact,
      conversationUserName, messagingEndUser, isLoading);

  @override
  String toString() {
    return 'ContactDetailsState(contactDetails: $contactDetails, contact: $contact, conversationUserName: $conversationUserName, messagingEndUser: $messagingEndUser, isLoading: $isLoading)';
  }
}

/// @nodoc
abstract mixin class $ContactDetailsStateCopyWith<$Res> {
  factory $ContactDetailsStateCopyWith(
          ContactDetailsState value, $Res Function(ContactDetailsState) _then) =
      _$ContactDetailsStateCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(ignore: true) ContactOrMeuAllDetails? contactDetails,
      Contact? contact,
      String? conversationUserName,
      MessagingEndUser? messagingEndUser,
      bool isLoading});

  $ContactCopyWith<$Res>? get contact;
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class _$ContactDetailsStateCopyWithImpl<$Res>
    implements $ContactDetailsStateCopyWith<$Res> {
  _$ContactDetailsStateCopyWithImpl(this._self, this._then);

  final ContactDetailsState _self;
  final $Res Function(ContactDetailsState) _then;

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactDetails = freezed,
    Object? contact = freezed,
    Object? conversationUserName = freezed,
    Object? messagingEndUser = freezed,
    Object? isLoading = null,
  }) {
    return _then(_self.copyWith(
      contactDetails: freezed == contactDetails
          ? _self.contactDetails
          : contactDetails // ignore: cast_nullable_to_non_nullable
              as ContactOrMeuAllDetails?,
      contact: freezed == contact
          ? _self.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      conversationUserName: freezed == conversationUserName
          ? _self.conversationUserName
          : conversationUserName // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUser: freezed == messagingEndUser
          ? _self.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get contact {
    if (_self.contact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_self.contact!, (value) {
      return _then(_self.copyWith(contact: value));
    });
  }

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser {
    if (_self.messagingEndUser == null) {
      return null;
    }

    return $MessagingEndUserCopyWith<$Res>(_self.messagingEndUser!, (value) {
      return _then(_self.copyWith(messagingEndUser: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ContactDetailsState].
extension ContactDetailsStatePatterns on ContactDetailsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactDetailsState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactDetailsState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactDetailsState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactDetailsState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactDetailsState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactDetailsState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true) ContactOrMeuAllDetails? contactDetails,
            Contact? contact,
            String? conversationUserName,
            MessagingEndUser? messagingEndUser,
            bool isLoading)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactDetailsState() when $default != null:
        return $default(
            _that.contactDetails,
            _that.contact,
            _that.conversationUserName,
            _that.messagingEndUser,
            _that.isLoading);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true) ContactOrMeuAllDetails? contactDetails,
            Contact? contact,
            String? conversationUserName,
            MessagingEndUser? messagingEndUser,
            bool isLoading)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactDetailsState():
        return $default(
            _that.contactDetails,
            _that.contact,
            _that.conversationUserName,
            _that.messagingEndUser,
            _that.isLoading);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(ignore: true) ContactOrMeuAllDetails? contactDetails,
            Contact? contact,
            String? conversationUserName,
            MessagingEndUser? messagingEndUser,
            bool isLoading)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactDetailsState() when $default != null:
        return $default(
            _that.contactDetails,
            _that.contact,
            _that.conversationUserName,
            _that.messagingEndUser,
            _that.isLoading);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactDetailsState extends ContactDetailsState {
  const _ContactDetailsState(
      {@JsonKey(ignore: true) this.contactDetails,
      this.contact,
      this.conversationUserName,
      this.messagingEndUser,
      this.isLoading = true})
      : super._();
  factory _ContactDetailsState.fromJson(Map<String, dynamic> json) =>
      _$ContactDetailsStateFromJson(json);

  @override
  @JsonKey(ignore: true)
  final ContactOrMeuAllDetails? contactDetails;
  @override
  final Contact? contact;
  @override
  final String? conversationUserName;
  @override
  final MessagingEndUser? messagingEndUser;
  @override
  @JsonKey()
  final bool isLoading;

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactDetailsStateCopyWith<_ContactDetailsState> get copyWith =>
      __$ContactDetailsStateCopyWithImpl<_ContactDetailsState>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactDetailsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactDetailsState &&
            (identical(other.contactDetails, contactDetails) ||
                other.contactDetails == contactDetails) &&
            (identical(other.contact, contact) || other.contact == contact) &&
            (identical(other.conversationUserName, conversationUserName) ||
                other.conversationUserName == conversationUserName) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactDetails, contact,
      conversationUserName, messagingEndUser, isLoading);

  @override
  String toString() {
    return 'ContactDetailsState(contactDetails: $contactDetails, contact: $contact, conversationUserName: $conversationUserName, messagingEndUser: $messagingEndUser, isLoading: $isLoading)';
  }
}

/// @nodoc
abstract mixin class _$ContactDetailsStateCopyWith<$Res>
    implements $ContactDetailsStateCopyWith<$Res> {
  factory _$ContactDetailsStateCopyWith(_ContactDetailsState value,
          $Res Function(_ContactDetailsState) _then) =
      __$ContactDetailsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(ignore: true) ContactOrMeuAllDetails? contactDetails,
      Contact? contact,
      String? conversationUserName,
      MessagingEndUser? messagingEndUser,
      bool isLoading});

  @override
  $ContactCopyWith<$Res>? get contact;
  @override
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class __$ContactDetailsStateCopyWithImpl<$Res>
    implements _$ContactDetailsStateCopyWith<$Res> {
  __$ContactDetailsStateCopyWithImpl(this._self, this._then);

  final _ContactDetailsState _self;
  final $Res Function(_ContactDetailsState) _then;

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contactDetails = freezed,
    Object? contact = freezed,
    Object? conversationUserName = freezed,
    Object? messagingEndUser = freezed,
    Object? isLoading = null,
  }) {
    return _then(_ContactDetailsState(
      contactDetails: freezed == contactDetails
          ? _self.contactDetails
          : contactDetails // ignore: cast_nullable_to_non_nullable
              as ContactOrMeuAllDetails?,
      contact: freezed == contact
          ? _self.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      conversationUserName: freezed == conversationUserName
          ? _self.conversationUserName
          : conversationUserName // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUser: freezed == messagingEndUser
          ? _self.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get contact {
    if (_self.contact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_self.contact!, (value) {
      return _then(_self.copyWith(contact: value));
    });
  }

  /// Create a copy of ContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser {
    if (_self.messagingEndUser == null) {
      return null;
    }

    return $MessagingEndUserCopyWith<$Res>(_self.messagingEndUser!, (value) {
      return _then(_self.copyWith(messagingEndUser: value));
    });
  }
}

// dart format on
