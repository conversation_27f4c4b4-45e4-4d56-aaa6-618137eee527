// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversations_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ConversationsState {
  @Json<PERSON>ey(ignore: true)
  @JsonKey(ignore: true)
  Map<SfId, Conversation> get conversations;
  @JsonKey(ignore: true)
  UiEvent<SfId>? get goToMessagingEndUserId;
  bool get showEnded;
  ConversationSortingOption? get sortingOption;
  dynamic get isLoading;
  @JsonKey(ignore: true)
  UiEvent<PostEndConversationsEvent>? get showEndAllConversationsConfirmation;
  QuickAction? get mainQuickAction;
  List<QuickAction>? get overflowQuickActions;
  @JsonKey(ignore: true)
  UiEvent<QuickActionException>? get quickActionException;
  bool get pushPermissionsGranted;

  /// Create a copy of ConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConversationsStateCopyWith<ConversationsState> get copyWith =>
      _$ConversationsStateCopyWithImpl<ConversationsState>(
          this as ConversationsState, _$identity);

  /// Serializes this ConversationsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ConversationsState &&
            const DeepCollectionEquality()
                .equals(other.conversations, conversations) &&
            (identical(other.goToMessagingEndUserId, goToMessagingEndUserId) ||
                other.goToMessagingEndUserId == goToMessagingEndUserId) &&
            (identical(other.showEnded, showEnded) ||
                other.showEnded == showEnded) &&
            (identical(other.sortingOption, sortingOption) ||
                other.sortingOption == sortingOption) &&
            const DeepCollectionEquality().equals(other.isLoading, isLoading) &&
            (identical(other.showEndAllConversationsConfirmation,
                    showEndAllConversationsConfirmation) ||
                other.showEndAllConversationsConfirmation ==
                    showEndAllConversationsConfirmation) &&
            (identical(other.mainQuickAction, mainQuickAction) ||
                other.mainQuickAction == mainQuickAction) &&
            const DeepCollectionEquality()
                .equals(other.overflowQuickActions, overflowQuickActions) &&
            (identical(other.quickActionException, quickActionException) ||
                other.quickActionException == quickActionException) &&
            (identical(other.pushPermissionsGranted, pushPermissionsGranted) ||
                other.pushPermissionsGranted == pushPermissionsGranted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(conversations),
      goToMessagingEndUserId,
      showEnded,
      sortingOption,
      const DeepCollectionEquality().hash(isLoading),
      showEndAllConversationsConfirmation,
      mainQuickAction,
      const DeepCollectionEquality().hash(overflowQuickActions),
      quickActionException,
      pushPermissionsGranted);

  @override
  String toString() {
    return 'ConversationsState(conversations: $conversations, goToMessagingEndUserId: $goToMessagingEndUserId, showEnded: $showEnded, sortingOption: $sortingOption, isLoading: $isLoading, showEndAllConversationsConfirmation: $showEndAllConversationsConfirmation, mainQuickAction: $mainQuickAction, overflowQuickActions: $overflowQuickActions, quickActionException: $quickActionException, pushPermissionsGranted: $pushPermissionsGranted)';
  }
}

/// @nodoc
abstract mixin class $ConversationsStateCopyWith<$Res> {
  factory $ConversationsStateCopyWith(
          ConversationsState value, $Res Function(ConversationsState) _then) =
      _$ConversationsStateCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(ignore: true)
      @JsonKey(ignore: true)
      Map<SfId, Conversation> conversations,
      @JsonKey(ignore: true) UiEvent<SfId>? goToMessagingEndUserId,
      bool showEnded,
      ConversationSortingOption? sortingOption,
      dynamic isLoading,
      @JsonKey(ignore: true)
      UiEvent<PostEndConversationsEvent>? showEndAllConversationsConfirmation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      @JsonKey(ignore: true)
      UiEvent<QuickActionException>? quickActionException,
      bool pushPermissionsGranted});

  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class _$ConversationsStateCopyWithImpl<$Res>
    implements $ConversationsStateCopyWith<$Res> {
  _$ConversationsStateCopyWithImpl(this._self, this._then);

  final ConversationsState _self;
  final $Res Function(ConversationsState) _then;

  /// Create a copy of ConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversations = null,
    Object? goToMessagingEndUserId = freezed,
    Object? showEnded = null,
    Object? sortingOption = freezed,
    Object? isLoading = freezed,
    Object? showEndAllConversationsConfirmation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
    Object? pushPermissionsGranted = null,
  }) {
    return _then(_self.copyWith(
      conversations: null == conversations
          ? _self.conversations
          : conversations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Conversation>,
      goToMessagingEndUserId: freezed == goToMessagingEndUserId
          ? _self.goToMessagingEndUserId
          : goToMessagingEndUserId // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      showEnded: null == showEnded
          ? _self.showEnded
          : showEnded // ignore: cast_nullable_to_non_nullable
              as bool,
      sortingOption: freezed == sortingOption
          ? _self.sortingOption
          : sortingOption // ignore: cast_nullable_to_non_nullable
              as ConversationSortingOption?,
      isLoading: freezed == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as dynamic,
      showEndAllConversationsConfirmation: freezed ==
              showEndAllConversationsConfirmation
          ? _self.showEndAllConversationsConfirmation
          : showEndAllConversationsConfirmation // ignore: cast_nullable_to_non_nullable
              as UiEvent<PostEndConversationsEvent>?,
      mainQuickAction: freezed == mainQuickAction
          ? _self.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _self.overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _self.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
      pushPermissionsGranted: null == pushPermissionsGranted
          ? _self.pushPermissionsGranted
          : pushPermissionsGranted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of ConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuickActionCopyWith<$Res>? get mainQuickAction {
    if (_self.mainQuickAction == null) {
      return null;
    }

    return $QuickActionCopyWith<$Res>(_self.mainQuickAction!, (value) {
      return _then(_self.copyWith(mainQuickAction: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ConversationsState].
extension ConversationsStatePatterns on ConversationsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ConversationsState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ConversationsState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ConversationsState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationsState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ConversationsState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationsState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true)
            @JsonKey(ignore: true)
            Map<SfId, Conversation> conversations,
            @JsonKey(ignore: true) UiEvent<SfId>? goToMessagingEndUserId,
            bool showEnded,
            ConversationSortingOption? sortingOption,
            dynamic isLoading,
            @JsonKey(ignore: true)
            UiEvent<PostEndConversationsEvent>?
                showEndAllConversationsConfirmation,
            QuickAction? mainQuickAction,
            List<QuickAction>? overflowQuickActions,
            @JsonKey(ignore: true)
            UiEvent<QuickActionException>? quickActionException,
            bool pushPermissionsGranted)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ConversationsState() when $default != null:
        return $default(
            _that.conversations,
            _that.goToMessagingEndUserId,
            _that.showEnded,
            _that.sortingOption,
            _that.isLoading,
            _that.showEndAllConversationsConfirmation,
            _that.mainQuickAction,
            _that.overflowQuickActions,
            _that.quickActionException,
            _that.pushPermissionsGranted);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true)
            @JsonKey(ignore: true)
            Map<SfId, Conversation> conversations,
            @JsonKey(ignore: true) UiEvent<SfId>? goToMessagingEndUserId,
            bool showEnded,
            ConversationSortingOption? sortingOption,
            dynamic isLoading,
            @JsonKey(ignore: true)
            UiEvent<PostEndConversationsEvent>?
                showEndAllConversationsConfirmation,
            QuickAction? mainQuickAction,
            List<QuickAction>? overflowQuickActions,
            @JsonKey(ignore: true)
            UiEvent<QuickActionException>? quickActionException,
            bool pushPermissionsGranted)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationsState():
        return $default(
            _that.conversations,
            _that.goToMessagingEndUserId,
            _that.showEnded,
            _that.sortingOption,
            _that.isLoading,
            _that.showEndAllConversationsConfirmation,
            _that.mainQuickAction,
            _that.overflowQuickActions,
            _that.quickActionException,
            _that.pushPermissionsGranted);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(ignore: true)
            @JsonKey(ignore: true)
            Map<SfId, Conversation> conversations,
            @JsonKey(ignore: true) UiEvent<SfId>? goToMessagingEndUserId,
            bool showEnded,
            ConversationSortingOption? sortingOption,
            dynamic isLoading,
            @JsonKey(ignore: true)
            UiEvent<PostEndConversationsEvent>?
                showEndAllConversationsConfirmation,
            QuickAction? mainQuickAction,
            List<QuickAction>? overflowQuickActions,
            @JsonKey(ignore: true)
            UiEvent<QuickActionException>? quickActionException,
            bool pushPermissionsGranted)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationsState() when $default != null:
        return $default(
            _that.conversations,
            _that.goToMessagingEndUserId,
            _that.showEnded,
            _that.sortingOption,
            _that.isLoading,
            _that.showEndAllConversationsConfirmation,
            _that.mainQuickAction,
            _that.overflowQuickActions,
            _that.quickActionException,
            _that.pushPermissionsGranted);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ConversationsState extends ConversationsState {
  const _ConversationsState(
      {@JsonKey(ignore: true)
      @JsonKey(ignore: true)
      final Map<SfId, Conversation> conversations = const {},
      @JsonKey(ignore: true) this.goToMessagingEndUserId,
      this.showEnded = false,
      this.sortingOption,
      this.isLoading = false,
      @JsonKey(ignore: true) this.showEndAllConversationsConfirmation,
      this.mainQuickAction,
      final List<QuickAction>? overflowQuickActions,
      @JsonKey(ignore: true) this.quickActionException,
      this.pushPermissionsGranted = true})
      : _conversations = conversations,
        _overflowQuickActions = overflowQuickActions,
        super._();
  factory _ConversationsState.fromJson(Map<String, dynamic> json) =>
      _$ConversationsStateFromJson(json);

  final Map<SfId, Conversation> _conversations;
  @override
  @JsonKey(ignore: true)
  @JsonKey(ignore: true)
  Map<SfId, Conversation> get conversations {
    if (_conversations is EqualUnmodifiableMapView) return _conversations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_conversations);
  }

  @override
  @JsonKey(ignore: true)
  final UiEvent<SfId>? goToMessagingEndUserId;
  @override
  @JsonKey()
  final bool showEnded;
  @override
  final ConversationSortingOption? sortingOption;
  @override
  @JsonKey()
  final dynamic isLoading;
  @override
  @JsonKey(ignore: true)
  final UiEvent<PostEndConversationsEvent>? showEndAllConversationsConfirmation;
  @override
  final QuickAction? mainQuickAction;
  final List<QuickAction>? _overflowQuickActions;
  @override
  List<QuickAction>? get overflowQuickActions {
    final value = _overflowQuickActions;
    if (value == null) return null;
    if (_overflowQuickActions is EqualUnmodifiableListView)
      return _overflowQuickActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(ignore: true)
  final UiEvent<QuickActionException>? quickActionException;
  @override
  @JsonKey()
  final bool pushPermissionsGranted;

  /// Create a copy of ConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConversationsStateCopyWith<_ConversationsState> get copyWith =>
      __$ConversationsStateCopyWithImpl<_ConversationsState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConversationsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ConversationsState &&
            const DeepCollectionEquality()
                .equals(other._conversations, _conversations) &&
            (identical(other.goToMessagingEndUserId, goToMessagingEndUserId) ||
                other.goToMessagingEndUserId == goToMessagingEndUserId) &&
            (identical(other.showEnded, showEnded) ||
                other.showEnded == showEnded) &&
            (identical(other.sortingOption, sortingOption) ||
                other.sortingOption == sortingOption) &&
            const DeepCollectionEquality().equals(other.isLoading, isLoading) &&
            (identical(other.showEndAllConversationsConfirmation,
                    showEndAllConversationsConfirmation) ||
                other.showEndAllConversationsConfirmation ==
                    showEndAllConversationsConfirmation) &&
            (identical(other.mainQuickAction, mainQuickAction) ||
                other.mainQuickAction == mainQuickAction) &&
            const DeepCollectionEquality()
                .equals(other._overflowQuickActions, _overflowQuickActions) &&
            (identical(other.quickActionException, quickActionException) ||
                other.quickActionException == quickActionException) &&
            (identical(other.pushPermissionsGranted, pushPermissionsGranted) ||
                other.pushPermissionsGranted == pushPermissionsGranted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_conversations),
      goToMessagingEndUserId,
      showEnded,
      sortingOption,
      const DeepCollectionEquality().hash(isLoading),
      showEndAllConversationsConfirmation,
      mainQuickAction,
      const DeepCollectionEquality().hash(_overflowQuickActions),
      quickActionException,
      pushPermissionsGranted);

  @override
  String toString() {
    return 'ConversationsState(conversations: $conversations, goToMessagingEndUserId: $goToMessagingEndUserId, showEnded: $showEnded, sortingOption: $sortingOption, isLoading: $isLoading, showEndAllConversationsConfirmation: $showEndAllConversationsConfirmation, mainQuickAction: $mainQuickAction, overflowQuickActions: $overflowQuickActions, quickActionException: $quickActionException, pushPermissionsGranted: $pushPermissionsGranted)';
  }
}

/// @nodoc
abstract mixin class _$ConversationsStateCopyWith<$Res>
    implements $ConversationsStateCopyWith<$Res> {
  factory _$ConversationsStateCopyWith(
          _ConversationsState value, $Res Function(_ConversationsState) _then) =
      __$ConversationsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(ignore: true)
      @JsonKey(ignore: true)
      Map<SfId, Conversation> conversations,
      @JsonKey(ignore: true) UiEvent<SfId>? goToMessagingEndUserId,
      bool showEnded,
      ConversationSortingOption? sortingOption,
      dynamic isLoading,
      @JsonKey(ignore: true)
      UiEvent<PostEndConversationsEvent>? showEndAllConversationsConfirmation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      @JsonKey(ignore: true)
      UiEvent<QuickActionException>? quickActionException,
      bool pushPermissionsGranted});

  @override
  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class __$ConversationsStateCopyWithImpl<$Res>
    implements _$ConversationsStateCopyWith<$Res> {
  __$ConversationsStateCopyWithImpl(this._self, this._then);

  final _ConversationsState _self;
  final $Res Function(_ConversationsState) _then;

  /// Create a copy of ConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? conversations = null,
    Object? goToMessagingEndUserId = freezed,
    Object? showEnded = null,
    Object? sortingOption = freezed,
    Object? isLoading = freezed,
    Object? showEndAllConversationsConfirmation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
    Object? pushPermissionsGranted = null,
  }) {
    return _then(_ConversationsState(
      conversations: null == conversations
          ? _self._conversations
          : conversations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Conversation>,
      goToMessagingEndUserId: freezed == goToMessagingEndUserId
          ? _self.goToMessagingEndUserId
          : goToMessagingEndUserId // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      showEnded: null == showEnded
          ? _self.showEnded
          : showEnded // ignore: cast_nullable_to_non_nullable
              as bool,
      sortingOption: freezed == sortingOption
          ? _self.sortingOption
          : sortingOption // ignore: cast_nullable_to_non_nullable
              as ConversationSortingOption?,
      isLoading: freezed == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as dynamic,
      showEndAllConversationsConfirmation: freezed ==
              showEndAllConversationsConfirmation
          ? _self.showEndAllConversationsConfirmation
          : showEndAllConversationsConfirmation // ignore: cast_nullable_to_non_nullable
              as UiEvent<PostEndConversationsEvent>?,
      mainQuickAction: freezed == mainQuickAction
          ? _self.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _self._overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _self.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
      pushPermissionsGranted: null == pushPermissionsGranted
          ? _self.pushPermissionsGranted
          : pushPermissionsGranted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of ConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuickActionCopyWith<$Res>? get mainQuickAction {
    if (_self.mainQuickAction == null) {
      return null;
    }

    return $QuickActionCopyWith<$Res>(_self.mainQuickAction!, (value) {
      return _then(_self.copyWith(mainQuickAction: value));
    });
  }
}

// dart format on
