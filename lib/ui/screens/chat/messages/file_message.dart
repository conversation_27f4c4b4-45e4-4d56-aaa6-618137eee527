import 'package:file_saver/file_saver.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:loggy/loggy.dart';
import 'package:universal_io/io.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/conversation_entry_model.dart';
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/use_cases/salesforce_data/salesforce_data_use_case.dart';
import 'package:x1440/utils/extensions/buildContext_extension.dart';
import 'package:x1440/utils/extensions/iterable_extension.dart';
import 'package:x1440/utils/string_utils.dart';

extension ConversationEntryAttachmentFileSaver on ConversationEntryAttachment {
  MimeType get fileSaverMimeType => ((MimeType.values
          .firstWhereOrNull((mimeType) => mimeType.name == this.mimeType)) ??
      MimeType.other);
  LinkDetails? get linkDetails => url == null ? null : LinkDetails(link: url!);
}

extension FileFileSaverMimeType on File {
  MimeType get fileSaverMimeType {
    String? extension = path.split('.').last;
    MimeType? mimeTypeMatch = MimeType.values
        .firstWhereOrNull((mimeType) => mimeType.name == extension);
    if (mimeTypeMatch != null) return mimeTypeMatch;
    if (extension == 'txt') return MimeType.text;
    return MimeType.other;
  }
}

// TODO: this is being used for network files & for local ... better handle these two cases
class FileMessage extends StatelessWidget with UiLoggy {
  final bool isAgent;
  final String text;
  final ConversationEntryAttachment? attachment;
  final File? file;

  const FileMessage(
      {super.key,
      this.attachment,
      this.file,
      this.isAgent = false,
      this.text = ''});

  /// for locally saved files
  void _saveFile() {
    loggy.info(
        'saving file: ${file?.path} as ${file?.path.split('/').last} with fileSaverMimeType: ${file?.fileSaverMimeType}');
    // Updated to match newer FileSaver API - now requires fileExtension parameter
    final fileName = file!.path.split('/').last;
    final fileExtension = fileName.contains('.') ? fileName.split('.').last : '';
    
    FileSaver.instance.saveAs(
      name: fileName.contains('.') ? fileName.substring(0, fileName.lastIndexOf('.')) : fileName,
      fileExtension: fileExtension,
      file: file,
      mimeType: file!.fileSaverMimeType,
    );
  }

  void _saveAttachment() async {
    if (attachment?.versionId == null) throw Exception();

    String fileName = attachment?.name ?? file?.path.split('/').last ?? 'file';

    List<String> fileParts = fileName.toString().split('.');
    if (fileParts.length < 2) {
      fileName = '$fileName.${extensionFromMimeType(attachment!.mimeType)}';
    }

    GetIt.instance<SalesforceDataUseCase>().saveContentVersionToLocalFile(
        attachment!.versionId!, fileName, attachment!.fileSaverMimeType);
  }

  /// function to open cupertino dialog with buttons to share or save the file
  void _showFileOptionsModal(BuildContext context) {
    showCupertinoModalPopup(
        builder: (BuildContext context) => CupertinoActionSheet(
              actions: <CupertinoActionSheetAction>[
                CupertinoActionSheetAction(
                  child: Text(S.of(context).file_save_file_options_save_cta),
                  onPressed: () {
                    if (file != null) _saveFile();
                    if (attachment?.linkDetails != null) _saveAttachment();

                    Navigator.pop(context);
                  },
                ),
                // CupertinoActionSheetAction(
                //   child: const Text('Share'),
                //   onPressed: () {
                //     // share file
                //     if (file?.path.isNotEmpty == true) {
                //       Share.shareXFiles([XFile(file!.path)]);
                //     }
                //     Navigator.pop(context);
                //   },
                // ),
              ],
              cancelButton: CupertinoActionSheetAction(
                child: Text(S.of(context).file_save_file_options_cancel_cta),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
        context: context);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        var focusNode = FocusNode();
        FocusScope.of(context).requestFocus(focusNode);
        focusNode.unfocus();
        focusNode.dispose();

        _showFileOptionsModal(context);
      },
      child: Container(
        margin: EdgeInsets.only(
          top: ThemeConstants.defaultPadding,
        ),
        padding: EdgeInsets.all(ThemeConstants.defaultPadding),
        decoration: BoxDecoration(
          color: !isAgent
              ? context.theme.colorScheme.surface
              : context.theme.colorScheme.primary,
          borderRadius: BorderRadius.circular(ThemeConstants.tightBorderRadius),
        ),
        child: Row(
          children: [
            Icon(
              Icons.file_present,
              color: !isAgent
                  ? Theme.of(context).textTheme.titleSmall!.color
                  : Colors.white,
            ),
            const SizedBox(width: 10),
            Flexible(
              child: Text(
                text,
                style: TextStyle(
                  color: !isAgent
                      ? Theme.of(context).textTheme.titleSmall!.color
                      : Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
