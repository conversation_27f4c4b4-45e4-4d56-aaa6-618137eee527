// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definition_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingDefinitionStatus {
  bool get mustUseDefinition;
  List<MessagingDefinition>? get definitions;

  /// Create a copy of MessagingDefinitionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingDefinitionStatusCopyWith<MessagingDefinitionStatus> get copyWith =>
      _$MessagingDefinitionStatusCopyWithImpl<MessagingDefinitionStatus>(
          this as MessagingDefinitionStatus, _$identity);

  /// Serializes this MessagingDefinitionStatus to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingDefinitionStatus &&
            (identical(other.mustUseDefinition, mustUseDefinition) ||
                other.mustUseDefinition == mustUseDefinition) &&
            const DeepCollectionEquality()
                .equals(other.definitions, definitions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, mustUseDefinition,
      const DeepCollectionEquality().hash(definitions));

  @override
  String toString() {
    return 'MessagingDefinitionStatus(mustUseDefinition: $mustUseDefinition, definitions: $definitions)';
  }
}

/// @nodoc
abstract mixin class $MessagingDefinitionStatusCopyWith<$Res> {
  factory $MessagingDefinitionStatusCopyWith(MessagingDefinitionStatus value,
          $Res Function(MessagingDefinitionStatus) _then) =
      _$MessagingDefinitionStatusCopyWithImpl;
  @useResult
  $Res call({bool mustUseDefinition, List<MessagingDefinition>? definitions});
}

/// @nodoc
class _$MessagingDefinitionStatusCopyWithImpl<$Res>
    implements $MessagingDefinitionStatusCopyWith<$Res> {
  _$MessagingDefinitionStatusCopyWithImpl(this._self, this._then);

  final MessagingDefinitionStatus _self;
  final $Res Function(MessagingDefinitionStatus) _then;

  /// Create a copy of MessagingDefinitionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mustUseDefinition = null,
    Object? definitions = freezed,
  }) {
    return _then(_self.copyWith(
      mustUseDefinition: null == mustUseDefinition
          ? _self.mustUseDefinition
          : mustUseDefinition // ignore: cast_nullable_to_non_nullable
              as bool,
      definitions: freezed == definitions
          ? _self.definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [MessagingDefinitionStatus].
extension MessagingDefinitionStatusPatterns on MessagingDefinitionStatus {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingDefinitionStatus value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionStatus() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingDefinitionStatus value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionStatus():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingDefinitionStatus value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionStatus() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool mustUseDefinition, List<MessagingDefinition>? definitions)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionStatus() when $default != null:
        return $default(_that.mustUseDefinition, _that.definitions);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool mustUseDefinition, List<MessagingDefinition>? definitions)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionStatus():
        return $default(_that.mustUseDefinition, _that.definitions);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool mustUseDefinition, List<MessagingDefinition>? definitions)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionStatus() when $default != null:
        return $default(_that.mustUseDefinition, _that.definitions);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingDefinitionStatus implements MessagingDefinitionStatus {
  const _MessagingDefinitionStatus(
      {required this.mustUseDefinition,
      final List<MessagingDefinition>? definitions})
      : _definitions = definitions;
  factory _MessagingDefinitionStatus.fromJson(Map<String, dynamic> json) =>
      _$MessagingDefinitionStatusFromJson(json);

  @override
  final bool mustUseDefinition;
  final List<MessagingDefinition>? _definitions;
  @override
  List<MessagingDefinition>? get definitions {
    final value = _definitions;
    if (value == null) return null;
    if (_definitions is EqualUnmodifiableListView) return _definitions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of MessagingDefinitionStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingDefinitionStatusCopyWith<_MessagingDefinitionStatus>
      get copyWith =>
          __$MessagingDefinitionStatusCopyWithImpl<_MessagingDefinitionStatus>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingDefinitionStatusToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingDefinitionStatus &&
            (identical(other.mustUseDefinition, mustUseDefinition) ||
                other.mustUseDefinition == mustUseDefinition) &&
            const DeepCollectionEquality()
                .equals(other._definitions, _definitions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, mustUseDefinition,
      const DeepCollectionEquality().hash(_definitions));

  @override
  String toString() {
    return 'MessagingDefinitionStatus(mustUseDefinition: $mustUseDefinition, definitions: $definitions)';
  }
}

/// @nodoc
abstract mixin class _$MessagingDefinitionStatusCopyWith<$Res>
    implements $MessagingDefinitionStatusCopyWith<$Res> {
  factory _$MessagingDefinitionStatusCopyWith(_MessagingDefinitionStatus value,
          $Res Function(_MessagingDefinitionStatus) _then) =
      __$MessagingDefinitionStatusCopyWithImpl;
  @override
  @useResult
  $Res call({bool mustUseDefinition, List<MessagingDefinition>? definitions});
}

/// @nodoc
class __$MessagingDefinitionStatusCopyWithImpl<$Res>
    implements _$MessagingDefinitionStatusCopyWith<$Res> {
  __$MessagingDefinitionStatusCopyWithImpl(this._self, this._then);

  final _MessagingDefinitionStatus _self;
  final $Res Function(_MessagingDefinitionStatus) _then;

  /// Create a copy of MessagingDefinitionStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? mustUseDefinition = null,
    Object? definitions = freezed,
  }) {
    return _then(_MessagingDefinitionStatus(
      mustUseDefinition: null == mustUseDefinition
          ? _self.mustUseDefinition
          : mustUseDefinition // ignore: cast_nullable_to_non_nullable
              as bool,
      definitions: freezed == definitions
          ? _self._definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
    ));
  }
}

// dart format on
