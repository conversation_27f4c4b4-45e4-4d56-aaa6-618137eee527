// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_definition_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingDefinitionStatus _$MessagingDefinitionStatusFromJson(Map json) =>
    $checkedCreate(
      '_MessagingDefinitionStatus',
      json,
      ($checkedConvert) {
        final val = _MessagingDefinitionStatus(
          mustUseDefinition:
              $checkedConvert('mustUseDefinition', (v) => v as bool),
          definitions: $checkedConvert(
              'definitions',
              (v) => (v as List<dynamic>?)
                  ?.map((e) => MessagingDefinition.fromJson(
                      Map<String, dynamic>.from(e as Map)))
                  .toList()),
        );
        return val;
      },
    );

Map<String, dynamic> _$MessagingDefinitionStatusToJson(
        _MessagingDefinitionStatus instance) =>
    <String, dynamic>{
      'mustUseDefinition': instance.mustUseDefinition,
      if (instance.definitions?.map((e) => e.toJson()).toList()
          case final value?)
        'definitions': value,
    };
