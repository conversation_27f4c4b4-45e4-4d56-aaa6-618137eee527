import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'agent_work.freezed.dart';
part 'agent_work.g.dart';

@freezed
abstract class AgentWork with _$AgentWork {
  const factory AgentWork({
    required SfId id,
    required SfId workTargetId
  }) = _AgentWork;

  factory AgentWork.fromJson(Map<String, dynamic> json) => _$AgentWorkFromJson(json);
}