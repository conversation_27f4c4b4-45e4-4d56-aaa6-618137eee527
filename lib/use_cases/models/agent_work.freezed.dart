// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'agent_work.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AgentWork {
  SfId get id;
  SfId get workTargetId;

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AgentWorkCopyWith<AgentWork> get copyWith =>
      _$AgentWorkCopyWithImpl<AgentWork>(this as AgentWork, _$identity);

  /// Serializes this AgentWork to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AgentWork &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, workTargetId);

  @override
  String toString() {
    return 'AgentWork(id: $id, workTargetId: $workTargetId)';
  }
}

/// @nodoc
abstract mixin class $AgentWorkCopyWith<$Res> {
  factory $AgentWorkCopyWith(AgentWork value, $Res Function(AgentWork) _then) =
      _$AgentWorkCopyWithImpl;
  @useResult
  $Res call({SfId id, SfId workTargetId});

  $SfIdCopyWith<$Res> get id;
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$AgentWorkCopyWithImpl<$Res> implements $AgentWorkCopyWith<$Res> {
  _$AgentWorkCopyWithImpl(this._self, this._then);

  final AgentWork _self;
  final $Res Function(AgentWork) _then;

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? workTargetId = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AgentWork].
extension AgentWorkPatterns on AgentWork {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AgentWork value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AgentWork() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AgentWork value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWork():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AgentWork value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWork() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(SfId id, SfId workTargetId)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AgentWork() when $default != null:
        return $default(_that.id, _that.workTargetId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(SfId id, SfId workTargetId) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWork():
        return $default(_that.id, _that.workTargetId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(SfId id, SfId workTargetId)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWork() when $default != null:
        return $default(_that.id, _that.workTargetId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AgentWork implements AgentWork {
  const _AgentWork({required this.id, required this.workTargetId});
  factory _AgentWork.fromJson(Map<String, dynamic> json) =>
      _$AgentWorkFromJson(json);

  @override
  final SfId id;
  @override
  final SfId workTargetId;

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AgentWorkCopyWith<_AgentWork> get copyWith =>
      __$AgentWorkCopyWithImpl<_AgentWork>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AgentWorkToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AgentWork &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, workTargetId);

  @override
  String toString() {
    return 'AgentWork(id: $id, workTargetId: $workTargetId)';
  }
}

/// @nodoc
abstract mixin class _$AgentWorkCopyWith<$Res>
    implements $AgentWorkCopyWith<$Res> {
  factory _$AgentWorkCopyWith(
          _AgentWork value, $Res Function(_AgentWork) _then) =
      __$AgentWorkCopyWithImpl;
  @override
  @useResult
  $Res call({SfId id, SfId workTargetId});

  @override
  $SfIdCopyWith<$Res> get id;
  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$AgentWorkCopyWithImpl<$Res> implements _$AgentWorkCopyWith<$Res> {
  __$AgentWorkCopyWithImpl(this._self, this._then);

  final _AgentWork _self;
  final $Res Function(_AgentWork) _then;

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? workTargetId = null,
  }) {
    return _then(_AgentWork(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of AgentWork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

// dart format on
