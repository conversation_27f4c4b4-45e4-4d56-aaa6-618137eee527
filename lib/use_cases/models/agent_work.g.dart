// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_work.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AgentWork _$AgentWorkFromJson(Map json) => $checkedCreate(
      '_AgentWork',
      json,
      ($checkedConvert) {
        final val = _AgentWork(
          id: $checkedConvert(
              'id', (v) => SfId.fromJson(Map<String, dynamic>.from(v as Map))),
          workTargetId: $checkedConvert('workTargetId',
              (v) => SfId.fromJson(Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$AgentWorkToJson(_AgentWork instance) =>
    <String, dynamic>{
      'id': instance.id.toJson(),
      'workTargetId': instance.workTargetId.toJson(),
    };
