// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Contact {
  @Json<PERSON>ey(name: 'Id')
  SfId? get id;
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @JsonKey(name: 'LastName')
  String? get lastName;
  @JsonKey(name: 'Title')
  String? get title;
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @JsonKey(name: 'Email')
  String? get email;

  /// fields to support Lak Participant model
  @JsonKey(name: 'ConsumerId')
  SfId? get consumerId;
  @JsonKey(name: 'UserDetails')
  Map<String, dynamic>? get userDetails;

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactCopyWith<Contact> get copyWith =>
      _$ContactCopyWithImpl<Contact>(this as Contact, _$identity);

  /// Serializes this Contact to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Contact &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.consumerId, consumerId) ||
                other.consumerId == consumerId) &&
            const DeepCollectionEquality()
                .equals(other.userDetails, userDetails));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      title,
      photoUrl,
      mobilePhone,
      email,
      consumerId,
      const DeepCollectionEquality().hash(userDetails));

  @override
  String toString() {
    return 'Contact(id: $id, firstName: $firstName, lastName: $lastName, title: $title, photoUrl: $photoUrl, mobilePhone: $mobilePhone, email: $email, consumerId: $consumerId, userDetails: $userDetails)';
  }
}

/// @nodoc
abstract mixin class $ContactCopyWith<$Res> {
  factory $ContactCopyWith(Contact value, $Res Function(Contact) _then) =
      _$ContactCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') SfId? id,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'ConsumerId') SfId? consumerId,
      @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails});

  $SfIdCopyWith<$Res>? get id;
  $SfIdCopyWith<$Res>? get consumerId;
}

/// @nodoc
class _$ContactCopyWithImpl<$Res> implements $ContactCopyWith<$Res> {
  _$ContactCopyWithImpl(this._self, this._then);

  final Contact _self;
  final $Res Function(Contact) _then;

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? title = freezed,
    Object? photoUrl = freezed,
    Object? mobilePhone = freezed,
    Object? email = freezed,
    Object? consumerId = freezed,
    Object? userDetails = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      consumerId: freezed == consumerId
          ? _self.consumerId
          : consumerId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      userDetails: freezed == userDetails
          ? _self.userDetails
          : userDetails // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get id {
    if (_self.id == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.id!, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get consumerId {
    if (_self.consumerId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.consumerId!, (value) {
      return _then(_self.copyWith(consumerId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [Contact].
extension ContactPatterns on Contact {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Contact value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Contact() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Contact value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Contact():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Contact value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Contact() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') SfId? id,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'ConsumerId') SfId? consumerId,
            @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Contact() when $default != null:
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.title,
            _that.photoUrl,
            _that.mobilePhone,
            _that.email,
            _that.consumerId,
            _that.userDetails);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') SfId? id,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'ConsumerId') SfId? consumerId,
            @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Contact():
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.title,
            _that.photoUrl,
            _that.mobilePhone,
            _that.email,
            _that.consumerId,
            _that.userDetails);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'Id') SfId? id,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'ConsumerId') SfId? consumerId,
            @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Contact() when $default != null:
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.title,
            _that.photoUrl,
            _that.mobilePhone,
            _that.email,
            _that.consumerId,
            _that.userDetails);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Contact extends Contact {
  const _Contact(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'Title') this.title,
      @JsonKey(name: 'PhotoUrl') this.photoUrl,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'ConsumerId') this.consumerId,
      @JsonKey(name: 'UserDetails') final Map<String, dynamic>? userDetails})
      : _userDetails = userDetails,
        super._();
  factory _Contact.fromJson(Map<String, dynamic> json) =>
      _$ContactFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final SfId? id;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'Title')
  final String? title;
  @override
  @JsonKey(name: 'PhotoUrl')
  final String? photoUrl;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'Email')
  final String? email;

  /// fields to support Lak Participant model
  @override
  @JsonKey(name: 'ConsumerId')
  final SfId? consumerId;
  final Map<String, dynamic>? _userDetails;
  @override
  @JsonKey(name: 'UserDetails')
  Map<String, dynamic>? get userDetails {
    final value = _userDetails;
    if (value == null) return null;
    if (_userDetails is EqualUnmodifiableMapView) return _userDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactCopyWith<_Contact> get copyWith =>
      __$ContactCopyWithImpl<_Contact>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Contact &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.consumerId, consumerId) ||
                other.consumerId == consumerId) &&
            const DeepCollectionEquality()
                .equals(other._userDetails, _userDetails));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      title,
      photoUrl,
      mobilePhone,
      email,
      consumerId,
      const DeepCollectionEquality().hash(_userDetails));

  @override
  String toString() {
    return 'Contact(id: $id, firstName: $firstName, lastName: $lastName, title: $title, photoUrl: $photoUrl, mobilePhone: $mobilePhone, email: $email, consumerId: $consumerId, userDetails: $userDetails)';
  }
}

/// @nodoc
abstract mixin class _$ContactCopyWith<$Res> implements $ContactCopyWith<$Res> {
  factory _$ContactCopyWith(_Contact value, $Res Function(_Contact) _then) =
      __$ContactCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') SfId? id,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'ConsumerId') SfId? consumerId,
      @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails});

  @override
  $SfIdCopyWith<$Res>? get id;
  @override
  $SfIdCopyWith<$Res>? get consumerId;
}

/// @nodoc
class __$ContactCopyWithImpl<$Res> implements _$ContactCopyWith<$Res> {
  __$ContactCopyWithImpl(this._self, this._then);

  final _Contact _self;
  final $Res Function(_Contact) _then;

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? title = freezed,
    Object? photoUrl = freezed,
    Object? mobilePhone = freezed,
    Object? email = freezed,
    Object? consumerId = freezed,
    Object? userDetails = freezed,
  }) {
    return _then(_Contact(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      consumerId: freezed == consumerId
          ? _self.consumerId
          : consumerId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      userDetails: freezed == userDetails
          ? _self._userDetails
          : userDetails // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get id {
    if (_self.id == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.id!, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get consumerId {
    if (_self.consumerId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.consumerId!, (value) {
      return _then(_self.copyWith(consumerId: value));
    });
  }
}

// dart format on
