// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Contact _$ContactFromJson(Map json) => $checkedCreate(
      '_Contact',
      json,
      ($checkedConvert) {
        final val = _Contact(
          id: $checkedConvert(
              'Id',
              (v) => v == null
                  ? null
                  : SfId.fromJson(Map<String, dynamic>.from(v as Map))),
          firstName: $checkedConvert('FirstName', (v) => v as String?),
          lastName: $checkedConvert('LastName', (v) => v as String?),
          title: $checkedConvert('Title', (v) => v as String?),
          photoUrl: $checkedConvert('PhotoUrl', (v) => v as String?),
          mobilePhone: $checkedConvert('MobilePhone', (v) => v as String?),
          email: $checkedConvert('Email', (v) => v as String?),
          consumerId: $checkedConvert(
              'ConsumerId',
              (v) => v == null
                  ? null
                  : SfId.fromJson(Map<String, dynamic>.from(v as Map))),
          userDetails: $checkedConvert(
              'UserDetails',
              (v) => (v as Map?)?.map(
                    (k, e) => MapEntry(k as String, e),
                  )),
        );
        return val;
      },
      fieldKeyMap: const {
        'id': 'Id',
        'firstName': 'FirstName',
        'lastName': 'LastName',
        'title': 'Title',
        'photoUrl': 'PhotoUrl',
        'mobilePhone': 'MobilePhone',
        'email': 'Email',
        'consumerId': 'ConsumerId',
        'userDetails': 'UserDetails'
      },
    );

Map<String, dynamic> _$ContactToJson(_Contact instance) => <String, dynamic>{
      if (instance.id?.toJson() case final value?) 'Id': value,
      if (instance.firstName case final value?) 'FirstName': value,
      if (instance.lastName case final value?) 'LastName': value,
      if (instance.title case final value?) 'Title': value,
      if (instance.photoUrl case final value?) 'PhotoUrl': value,
      if (instance.mobilePhone case final value?) 'MobilePhone': value,
      if (instance.email case final value?) 'Email': value,
      if (instance.consumerId?.toJson() case final value?) 'ConsumerId': value,
      if (instance.userDetails case final value?) 'UserDetails': value,
    };
