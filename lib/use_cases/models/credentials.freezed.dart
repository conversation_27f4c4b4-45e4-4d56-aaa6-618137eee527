// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credentials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Credentials {
  String? get userId;
  String? get instanceUrl;
  String? get accessToken; // This is the SalesForce access token
  String? get orgId;
  String? get authorizationToken; //This is the Shim Service access token
  String? get webSocketUrl;
  String? get sessionToken;
  String? get sessionId;
  String? get refreshToken;
  String? get devicePushToken;
  int? get authorizationTokenExpirationTime;
  int? get sessionExpirationTime;

  /// Create a copy of Credentials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CredentialsCopyWith<Credentials> get copyWith =>
      _$CredentialsCopyWithImpl<Credentials>(this as Credentials, _$identity);

  /// Serializes this Credentials to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Credentials &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.authorizationToken, authorizationToken) ||
                other.authorizationToken == authorizationToken) &&
            (identical(other.webSocketUrl, webSocketUrl) ||
                other.webSocketUrl == webSocketUrl) &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.devicePushToken, devicePushToken) ||
                other.devicePushToken == devicePushToken) &&
            (identical(other.authorizationTokenExpirationTime,
                    authorizationTokenExpirationTime) ||
                other.authorizationTokenExpirationTime ==
                    authorizationTokenExpirationTime) &&
            (identical(other.sessionExpirationTime, sessionExpirationTime) ||
                other.sessionExpirationTime == sessionExpirationTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      instanceUrl,
      accessToken,
      orgId,
      authorizationToken,
      webSocketUrl,
      sessionToken,
      sessionId,
      refreshToken,
      devicePushToken,
      authorizationTokenExpirationTime,
      sessionExpirationTime);

  @override
  String toString() {
    return 'Credentials(userId: $userId, instanceUrl: $instanceUrl, accessToken: $accessToken, orgId: $orgId, authorizationToken: $authorizationToken, webSocketUrl: $webSocketUrl, sessionToken: $sessionToken, sessionId: $sessionId, refreshToken: $refreshToken, devicePushToken: $devicePushToken, authorizationTokenExpirationTime: $authorizationTokenExpirationTime, sessionExpirationTime: $sessionExpirationTime)';
  }
}

/// @nodoc
abstract mixin class $CredentialsCopyWith<$Res> {
  factory $CredentialsCopyWith(
          Credentials value, $Res Function(Credentials) _then) =
      _$CredentialsCopyWithImpl;
  @useResult
  $Res call(
      {String? userId,
      String? instanceUrl,
      String? accessToken,
      String? orgId,
      String? authorizationToken,
      String? webSocketUrl,
      String? sessionToken,
      String? sessionId,
      String? refreshToken,
      String? devicePushToken,
      int? authorizationTokenExpirationTime,
      int? sessionExpirationTime});
}

/// @nodoc
class _$CredentialsCopyWithImpl<$Res> implements $CredentialsCopyWith<$Res> {
  _$CredentialsCopyWithImpl(this._self, this._then);

  final Credentials _self;
  final $Res Function(Credentials) _then;

  /// Create a copy of Credentials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? instanceUrl = freezed,
    Object? accessToken = freezed,
    Object? orgId = freezed,
    Object? authorizationToken = freezed,
    Object? webSocketUrl = freezed,
    Object? sessionToken = freezed,
    Object? sessionId = freezed,
    Object? refreshToken = freezed,
    Object? devicePushToken = freezed,
    Object? authorizationTokenExpirationTime = freezed,
    Object? sessionExpirationTime = freezed,
  }) {
    return _then(_self.copyWith(
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationToken: freezed == authorizationToken
          ? _self.authorizationToken
          : authorizationToken // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: freezed == webSocketUrl
          ? _self.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionToken: freezed == sessionToken
          ? _self.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      devicePushToken: freezed == devicePushToken
          ? _self.devicePushToken
          : devicePushToken // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationTokenExpirationTime: freezed ==
              authorizationTokenExpirationTime
          ? _self.authorizationTokenExpirationTime
          : authorizationTokenExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationTime: freezed == sessionExpirationTime
          ? _self.sessionExpirationTime
          : sessionExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// Adds pattern-matching-related methods to [Credentials].
extension CredentialsPatterns on Credentials {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Credentials value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Credentials() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Credentials value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Credentials():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Credentials value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Credentials() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? userId,
            String? instanceUrl,
            String? accessToken,
            String? orgId,
            String? authorizationToken,
            String? webSocketUrl,
            String? sessionToken,
            String? sessionId,
            String? refreshToken,
            String? devicePushToken,
            int? authorizationTokenExpirationTime,
            int? sessionExpirationTime)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Credentials() when $default != null:
        return $default(
            _that.userId,
            _that.instanceUrl,
            _that.accessToken,
            _that.orgId,
            _that.authorizationToken,
            _that.webSocketUrl,
            _that.sessionToken,
            _that.sessionId,
            _that.refreshToken,
            _that.devicePushToken,
            _that.authorizationTokenExpirationTime,
            _that.sessionExpirationTime);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? userId,
            String? instanceUrl,
            String? accessToken,
            String? orgId,
            String? authorizationToken,
            String? webSocketUrl,
            String? sessionToken,
            String? sessionId,
            String? refreshToken,
            String? devicePushToken,
            int? authorizationTokenExpirationTime,
            int? sessionExpirationTime)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Credentials():
        return $default(
            _that.userId,
            _that.instanceUrl,
            _that.accessToken,
            _that.orgId,
            _that.authorizationToken,
            _that.webSocketUrl,
            _that.sessionToken,
            _that.sessionId,
            _that.refreshToken,
            _that.devicePushToken,
            _that.authorizationTokenExpirationTime,
            _that.sessionExpirationTime);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? userId,
            String? instanceUrl,
            String? accessToken,
            String? orgId,
            String? authorizationToken,
            String? webSocketUrl,
            String? sessionToken,
            String? sessionId,
            String? refreshToken,
            String? devicePushToken,
            int? authorizationTokenExpirationTime,
            int? sessionExpirationTime)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Credentials() when $default != null:
        return $default(
            _that.userId,
            _that.instanceUrl,
            _that.accessToken,
            _that.orgId,
            _that.authorizationToken,
            _that.webSocketUrl,
            _that.sessionToken,
            _that.sessionId,
            _that.refreshToken,
            _that.devicePushToken,
            _that.authorizationTokenExpirationTime,
            _that.sessionExpirationTime);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Credentials extends Credentials {
  const _Credentials(
      {this.userId,
      this.instanceUrl,
      this.accessToken,
      this.orgId,
      this.authorizationToken,
      this.webSocketUrl,
      this.sessionToken,
      this.sessionId,
      this.refreshToken,
      this.devicePushToken,
      this.authorizationTokenExpirationTime,
      this.sessionExpirationTime})
      : super._();
  factory _Credentials.fromJson(Map<String, dynamic> json) =>
      _$CredentialsFromJson(json);

  @override
  final String? userId;
  @override
  final String? instanceUrl;
  @override
  final String? accessToken;
// This is the SalesForce access token
  @override
  final String? orgId;
  @override
  final String? authorizationToken;
//This is the Shim Service access token
  @override
  final String? webSocketUrl;
  @override
  final String? sessionToken;
  @override
  final String? sessionId;
  @override
  final String? refreshToken;
  @override
  final String? devicePushToken;
  @override
  final int? authorizationTokenExpirationTime;
  @override
  final int? sessionExpirationTime;

  /// Create a copy of Credentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CredentialsCopyWith<_Credentials> get copyWith =>
      __$CredentialsCopyWithImpl<_Credentials>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CredentialsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Credentials &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.authorizationToken, authorizationToken) ||
                other.authorizationToken == authorizationToken) &&
            (identical(other.webSocketUrl, webSocketUrl) ||
                other.webSocketUrl == webSocketUrl) &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.devicePushToken, devicePushToken) ||
                other.devicePushToken == devicePushToken) &&
            (identical(other.authorizationTokenExpirationTime,
                    authorizationTokenExpirationTime) ||
                other.authorizationTokenExpirationTime ==
                    authorizationTokenExpirationTime) &&
            (identical(other.sessionExpirationTime, sessionExpirationTime) ||
                other.sessionExpirationTime == sessionExpirationTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      instanceUrl,
      accessToken,
      orgId,
      authorizationToken,
      webSocketUrl,
      sessionToken,
      sessionId,
      refreshToken,
      devicePushToken,
      authorizationTokenExpirationTime,
      sessionExpirationTime);

  @override
  String toString() {
    return 'Credentials(userId: $userId, instanceUrl: $instanceUrl, accessToken: $accessToken, orgId: $orgId, authorizationToken: $authorizationToken, webSocketUrl: $webSocketUrl, sessionToken: $sessionToken, sessionId: $sessionId, refreshToken: $refreshToken, devicePushToken: $devicePushToken, authorizationTokenExpirationTime: $authorizationTokenExpirationTime, sessionExpirationTime: $sessionExpirationTime)';
  }
}

/// @nodoc
abstract mixin class _$CredentialsCopyWith<$Res>
    implements $CredentialsCopyWith<$Res> {
  factory _$CredentialsCopyWith(
          _Credentials value, $Res Function(_Credentials) _then) =
      __$CredentialsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? userId,
      String? instanceUrl,
      String? accessToken,
      String? orgId,
      String? authorizationToken,
      String? webSocketUrl,
      String? sessionToken,
      String? sessionId,
      String? refreshToken,
      String? devicePushToken,
      int? authorizationTokenExpirationTime,
      int? sessionExpirationTime});
}

/// @nodoc
class __$CredentialsCopyWithImpl<$Res> implements _$CredentialsCopyWith<$Res> {
  __$CredentialsCopyWithImpl(this._self, this._then);

  final _Credentials _self;
  final $Res Function(_Credentials) _then;

  /// Create a copy of Credentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = freezed,
    Object? instanceUrl = freezed,
    Object? accessToken = freezed,
    Object? orgId = freezed,
    Object? authorizationToken = freezed,
    Object? webSocketUrl = freezed,
    Object? sessionToken = freezed,
    Object? sessionId = freezed,
    Object? refreshToken = freezed,
    Object? devicePushToken = freezed,
    Object? authorizationTokenExpirationTime = freezed,
    Object? sessionExpirationTime = freezed,
  }) {
    return _then(_Credentials(
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationToken: freezed == authorizationToken
          ? _self.authorizationToken
          : authorizationToken // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: freezed == webSocketUrl
          ? _self.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionToken: freezed == sessionToken
          ? _self.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      devicePushToken: freezed == devicePushToken
          ? _self.devicePushToken
          : devicePushToken // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationTokenExpirationTime: freezed ==
              authorizationTokenExpirationTime
          ? _self.authorizationTokenExpirationTime
          : authorizationTokenExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationTime: freezed == sessionExpirationTime
          ? _self.sessionExpirationTime
          : sessionExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on
