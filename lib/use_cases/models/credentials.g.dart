// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credentials.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Credentials _$CredentialsFromJson(Map json) => $checkedCreate(
      '_Credentials',
      json,
      ($checkedConvert) {
        final val = _Credentials(
          userId: $checkedConvert('userId', (v) => v as String?),
          instanceUrl: $checkedConvert('instanceUrl', (v) => v as String?),
          accessToken: $checkedConvert('accessToken', (v) => v as String?),
          orgId: $checkedConvert('orgId', (v) => v as String?),
          authorizationToken:
              $checkedConvert('authorizationToken', (v) => v as String?),
          webSocketUrl: $checkedConvert('webSocketUrl', (v) => v as String?),
          sessionToken: $checkedConvert('sessionToken', (v) => v as String?),
          sessionId: $checkedConvert('sessionId', (v) => v as String?),
          refreshToken: $checkedConvert('refreshToken', (v) => v as String?),
          devicePushToken:
              $checkedConvert('devicePushToken', (v) => v as String?),
          authorizationTokenExpirationTime: $checkedConvert(
              'authorizationTokenExpirationTime', (v) => (v as num?)?.toInt()),
          sessionExpirationTime: $checkedConvert(
              'sessionExpirationTime', (v) => (v as num?)?.toInt()),
        );
        return val;
      },
    );

Map<String, dynamic> _$CredentialsToJson(_Credentials instance) =>
    <String, dynamic>{
      if (instance.userId case final value?) 'userId': value,
      if (instance.instanceUrl case final value?) 'instanceUrl': value,
      if (instance.accessToken case final value?) 'accessToken': value,
      if (instance.orgId case final value?) 'orgId': value,
      if (instance.authorizationToken case final value?)
        'authorizationToken': value,
      if (instance.webSocketUrl case final value?) 'webSocketUrl': value,
      if (instance.sessionToken case final value?) 'sessionToken': value,
      if (instance.sessionId case final value?) 'sessionId': value,
      if (instance.refreshToken case final value?) 'refreshToken': value,
      if (instance.devicePushToken case final value?) 'devicePushToken': value,
      if (instance.authorizationTokenExpirationTime case final value?)
        'authorizationTokenExpirationTime': value,
      if (instance.sessionExpirationTime case final value?)
        'sessionExpirationTime': value,
    };
