// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_channel.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingChannel {
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  SfId get id;
  @JsonKey(name: 'MessageType')
  String? get messageType;
  @JsonKey(name: 'DeveloperName')
  String? get developerName;
  @JsonKey(name: 'MasterLabel')
  String? get masterLabel;
  @JsonKey(name: 'MessagingPlatformKey')
  String? get messagingPlatformKey;
  @JsonKey(name: 'PlatformType')
  String? get platformType;
  @JsonKey(name: 'Language')
  String? get language;
  @JsonKey(name: 'IsoCountryCode')
  String? get isoCountryCode;
  @JsonKey(name: 'IsActive')
  bool? get isActive;
  @JsonKey(name: 'isDeleted')
  bool? get isDeleted;

  /// Create a copy of MessagingChannel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingChannelCopyWith<MessagingChannel> get copyWith =>
      _$MessagingChannelCopyWithImpl<MessagingChannel>(
          this as MessagingChannel, _$identity);

  /// Serializes this MessagingChannel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingChannel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.developerName, developerName) ||
                other.developerName == developerName) &&
            (identical(other.masterLabel, masterLabel) ||
                other.masterLabel == masterLabel) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.platformType, platformType) ||
                other.platformType == platformType) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.isoCountryCode, isoCountryCode) ||
                other.isoCountryCode == isoCountryCode) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      messageType,
      developerName,
      masterLabel,
      messagingPlatformKey,
      platformType,
      language,
      isoCountryCode,
      isActive,
      isDeleted);

  @override
  String toString() {
    return 'MessagingChannel(id: $id, messageType: $messageType, developerName: $developerName, masterLabel: $masterLabel, messagingPlatformKey: $messagingPlatformKey, platformType: $platformType, language: $language, isoCountryCode: $isoCountryCode, isActive: $isActive, isDeleted: $isDeleted)';
  }
}

/// @nodoc
abstract mixin class $MessagingChannelCopyWith<$Res> {
  factory $MessagingChannelCopyWith(
          MessagingChannel value, $Res Function(MessagingChannel) _then) =
      _$MessagingChannelCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'MessageType') String? messageType,
      @JsonKey(name: 'DeveloperName') String? developerName,
      @JsonKey(name: 'MasterLabel') String? masterLabel,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'PlatformType') String? platformType,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'IsoCountryCode') String? isoCountryCode,
      @JsonKey(name: 'IsActive') bool? isActive,
      @JsonKey(name: 'isDeleted') bool? isDeleted});

  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class _$MessagingChannelCopyWithImpl<$Res>
    implements $MessagingChannelCopyWith<$Res> {
  _$MessagingChannelCopyWithImpl(this._self, this._then);

  final MessagingChannel _self;
  final $Res Function(MessagingChannel) _then;

  /// Create a copy of MessagingChannel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageType = freezed,
    Object? developerName = freezed,
    Object? masterLabel = freezed,
    Object? messagingPlatformKey = freezed,
    Object? platformType = freezed,
    Object? language = freezed,
    Object? isoCountryCode = freezed,
    Object? isActive = freezed,
    Object? isDeleted = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: freezed == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      developerName: freezed == developerName
          ? _self.developerName
          : developerName // ignore: cast_nullable_to_non_nullable
              as String?,
      masterLabel: freezed == masterLabel
          ? _self.masterLabel
          : masterLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _self.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      platformType: freezed == platformType
          ? _self.platformType
          : platformType // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      isoCountryCode: freezed == isoCountryCode
          ? _self.isoCountryCode
          : isoCountryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _self.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDeleted: freezed == isDeleted
          ? _self.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of MessagingChannel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingChannel].
extension MessagingChannelPatterns on MessagingChannel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingChannel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingChannel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingChannel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingChannel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
            @JsonKey(name: 'MessageType') String? messageType,
            @JsonKey(name: 'DeveloperName') String? developerName,
            @JsonKey(name: 'MasterLabel') String? masterLabel,
            @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
            @JsonKey(name: 'PlatformType') String? platformType,
            @JsonKey(name: 'Language') String? language,
            @JsonKey(name: 'IsoCountryCode') String? isoCountryCode,
            @JsonKey(name: 'IsActive') bool? isActive,
            @JsonKey(name: 'isDeleted') bool? isDeleted)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingChannel() when $default != null:
        return $default(
            _that.id,
            _that.messageType,
            _that.developerName,
            _that.masterLabel,
            _that.messagingPlatformKey,
            _that.platformType,
            _that.language,
            _that.isoCountryCode,
            _that.isActive,
            _that.isDeleted);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
            @JsonKey(name: 'MessageType') String? messageType,
            @JsonKey(name: 'DeveloperName') String? developerName,
            @JsonKey(name: 'MasterLabel') String? masterLabel,
            @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
            @JsonKey(name: 'PlatformType') String? platformType,
            @JsonKey(name: 'Language') String? language,
            @JsonKey(name: 'IsoCountryCode') String? isoCountryCode,
            @JsonKey(name: 'IsActive') bool? isActive,
            @JsonKey(name: 'isDeleted') bool? isDeleted)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannel():
        return $default(
            _that.id,
            _that.messageType,
            _that.developerName,
            _that.masterLabel,
            _that.messagingPlatformKey,
            _that.platformType,
            _that.language,
            _that.isoCountryCode,
            _that.isActive,
            _that.isDeleted);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
            @JsonKey(name: 'MessageType') String? messageType,
            @JsonKey(name: 'DeveloperName') String? developerName,
            @JsonKey(name: 'MasterLabel') String? masterLabel,
            @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
            @JsonKey(name: 'PlatformType') String? platformType,
            @JsonKey(name: 'Language') String? language,
            @JsonKey(name: 'IsoCountryCode') String? isoCountryCode,
            @JsonKey(name: 'IsActive') bool? isActive,
            @JsonKey(name: 'isDeleted') bool? isDeleted)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannel() when $default != null:
        return $default(
            _that.id,
            _that.messageType,
            _that.developerName,
            _that.masterLabel,
            _that.messagingPlatformKey,
            _that.platformType,
            _that.language,
            _that.isoCountryCode,
            _that.isActive,
            _that.isDeleted);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingChannel extends MessagingChannel {
  const _MessagingChannel(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() required this.id,
      @JsonKey(name: 'MessageType') this.messageType,
      @JsonKey(name: 'DeveloperName') this.developerName,
      @JsonKey(name: 'MasterLabel') this.masterLabel,
      @JsonKey(name: 'MessagingPlatformKey') this.messagingPlatformKey,
      @JsonKey(name: 'PlatformType') this.platformType,
      @JsonKey(name: 'Language') this.language,
      @JsonKey(name: 'IsoCountryCode') this.isoCountryCode,
      @JsonKey(name: 'IsActive') this.isActive,
      @JsonKey(name: 'isDeleted') this.isDeleted})
      : super._();
  factory _MessagingChannel.fromJson(Map<String, dynamic> json) =>
      _$MessagingChannelFromJson(json);

  @override
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  final SfId id;
  @override
  @JsonKey(name: 'MessageType')
  final String? messageType;
  @override
  @JsonKey(name: 'DeveloperName')
  final String? developerName;
  @override
  @JsonKey(name: 'MasterLabel')
  final String? masterLabel;
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  final String? messagingPlatformKey;
  @override
  @JsonKey(name: 'PlatformType')
  final String? platformType;
  @override
  @JsonKey(name: 'Language')
  final String? language;
  @override
  @JsonKey(name: 'IsoCountryCode')
  final String? isoCountryCode;
  @override
  @JsonKey(name: 'IsActive')
  final bool? isActive;
  @override
  @JsonKey(name: 'isDeleted')
  final bool? isDeleted;

  /// Create a copy of MessagingChannel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingChannelCopyWith<_MessagingChannel> get copyWith =>
      __$MessagingChannelCopyWithImpl<_MessagingChannel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingChannelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingChannel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.developerName, developerName) ||
                other.developerName == developerName) &&
            (identical(other.masterLabel, masterLabel) ||
                other.masterLabel == masterLabel) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.platformType, platformType) ||
                other.platformType == platformType) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.isoCountryCode, isoCountryCode) ||
                other.isoCountryCode == isoCountryCode) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      messageType,
      developerName,
      masterLabel,
      messagingPlatformKey,
      platformType,
      language,
      isoCountryCode,
      isActive,
      isDeleted);

  @override
  String toString() {
    return 'MessagingChannel(id: $id, messageType: $messageType, developerName: $developerName, masterLabel: $masterLabel, messagingPlatformKey: $messagingPlatformKey, platformType: $platformType, language: $language, isoCountryCode: $isoCountryCode, isActive: $isActive, isDeleted: $isDeleted)';
  }
}

/// @nodoc
abstract mixin class _$MessagingChannelCopyWith<$Res>
    implements $MessagingChannelCopyWith<$Res> {
  factory _$MessagingChannelCopyWith(
          _MessagingChannel value, $Res Function(_MessagingChannel) _then) =
      __$MessagingChannelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'MessageType') String? messageType,
      @JsonKey(name: 'DeveloperName') String? developerName,
      @JsonKey(name: 'MasterLabel') String? masterLabel,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'PlatformType') String? platformType,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'IsoCountryCode') String? isoCountryCode,
      @JsonKey(name: 'IsActive') bool? isActive,
      @JsonKey(name: 'isDeleted') bool? isDeleted});

  @override
  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class __$MessagingChannelCopyWithImpl<$Res>
    implements _$MessagingChannelCopyWith<$Res> {
  __$MessagingChannelCopyWithImpl(this._self, this._then);

  final _MessagingChannel _self;
  final $Res Function(_MessagingChannel) _then;

  /// Create a copy of MessagingChannel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? messageType = freezed,
    Object? developerName = freezed,
    Object? masterLabel = freezed,
    Object? messagingPlatformKey = freezed,
    Object? platformType = freezed,
    Object? language = freezed,
    Object? isoCountryCode = freezed,
    Object? isActive = freezed,
    Object? isDeleted = freezed,
  }) {
    return _then(_MessagingChannel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: freezed == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      developerName: freezed == developerName
          ? _self.developerName
          : developerName // ignore: cast_nullable_to_non_nullable
              as String?,
      masterLabel: freezed == masterLabel
          ? _self.masterLabel
          : masterLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _self.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      platformType: freezed == platformType
          ? _self.platformType
          : platformType // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      isoCountryCode: freezed == isoCountryCode
          ? _self.isoCountryCode
          : isoCountryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _self.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDeleted: freezed == isDeleted
          ? _self.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of MessagingChannel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

// dart format on
