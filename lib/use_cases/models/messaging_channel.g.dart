// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_channel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingChannel _$MessagingChannelFromJson(Map json) => $checkedCreate(
      '_MessagingChannel',
      json,
      ($checkedConvert) {
        final val = _MessagingChannel(
          id: $checkedConvert(
              'Id', (v) => const ParseSfIdConverter().fromJson(v)),
          messageType: $checkedConvert('MessageType', (v) => v as String?),
          developerName: $checkedConvert('DeveloperName', (v) => v as String?),
          masterLabel: $checkedConvert('MasterLabel', (v) => v as String?),
          messagingPlatformKey:
              $checkedConvert('MessagingPlatformKey', (v) => v as String?),
          platformType: $checkedConvert('PlatformType', (v) => v as String?),
          language: $checkedConvert('Language', (v) => v as String?),
          isoCountryCode:
              $checkedConvert('IsoCountryCode', (v) => v as String?),
          isActive: $checkedConvert('IsActive', (v) => v as bool?),
          isDeleted: $checkedConvert('isDeleted', (v) => v as bool?),
        );
        return val;
      },
      fieldKeyMap: const {
        'id': 'Id',
        'messageType': 'MessageType',
        'developerName': 'DeveloperName',
        'masterLabel': 'MasterLabel',
        'messagingPlatformKey': 'MessagingPlatformKey',
        'platformType': 'PlatformType',
        'language': 'Language',
        'isoCountryCode': 'IsoCountryCode',
        'isActive': 'IsActive'
      },
    );

Map<String, dynamic> _$MessagingChannelToJson(_MessagingChannel instance) =>
    <String, dynamic>{
      if (const ParseSfIdConverter().toJson(instance.id) case final value?)
        'Id': value,
      if (instance.messageType case final value?) 'MessageType': value,
      if (instance.developerName case final value?) 'DeveloperName': value,
      if (instance.masterLabel case final value?) 'MasterLabel': value,
      if (instance.messagingPlatformKey case final value?)
        'MessagingPlatformKey': value,
      if (instance.platformType case final value?) 'PlatformType': value,
      if (instance.language case final value?) 'Language': value,
      if (instance.isoCountryCode case final value?) 'IsoCountryCode': value,
      if (instance.isActive case final value?) 'IsActive': value,
      if (instance.isDeleted case final value?) 'isDeleted': value,
    };
