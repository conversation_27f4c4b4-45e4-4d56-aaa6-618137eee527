// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingEndUser {
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  SfId get id; // @JsonKey(name: 'AccountId') String?  accountId,
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId?
      get contactId; // @JsonKey(name: 'HasInitialResponseSent') bool?  hasInitialResponseSent,
// @JsonKey(name: 'IsFullyOptedIn') bool?  isFullyOptedIn,
// @JsonKey(name: 'IsOptedOut') bool?  isOptedOut,
// @JsonKey(name: 'IsoCountryCode') String?  isoCountryCode,
// @JsonKey(name: 'MessageType') String?  messageType,
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  SfId?
      get messagingChannelId; // @JsonKey(name: 'MessagingConsentStatus') String?  messagingConsentStatus,
  @JsonKey(name: 'MessagingPlatformKey')
  String? get messagingPlatformKey;
  @JsonKey(name: 'Name')
  String? get name; // @JsonKey(name: 'OwnerId') String?  ownerId,
  @JsonKey(name: 'ProfilePictureUrl')
  String? get profilePictureUrl;

  /// items below are from the 'contact' object but were previously combined with this as such, these are added to support legacy code
  @Deprecated('legacy')
  String? get contactPictureUrl;
  @Deprecated('legacy')
  String? get contactEmail;
  @Deprecated('legacy')
  String? get contactName;
  @Deprecated('legacy')
  String? get contactMobilePhone;
  @Deprecated('legacy')
  String? get userFullPhotoUrl;

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingEndUserCopyWith<MessagingEndUser> get copyWith =>
      _$MessagingEndUserCopyWithImpl<MessagingEndUser>(
          this as MessagingEndUser, _$identity);

  /// Serializes this MessagingEndUser to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingEndUser &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.contactPictureUrl, contactPictureUrl) ||
                other.contactPictureUrl == contactPictureUrl) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail) &&
            (identical(other.contactName, contactName) ||
                other.contactName == contactName) &&
            (identical(other.contactMobilePhone, contactMobilePhone) ||
                other.contactMobilePhone == contactMobilePhone) &&
            (identical(other.userFullPhotoUrl, userFullPhotoUrl) ||
                other.userFullPhotoUrl == userFullPhotoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      contactId,
      messagingChannelId,
      messagingPlatformKey,
      name,
      profilePictureUrl,
      contactPictureUrl,
      contactEmail,
      contactName,
      contactMobilePhone,
      userFullPhotoUrl);

  @override
  String toString() {
    return 'MessagingEndUser(id: $id, contactId: $contactId, messagingChannelId: $messagingChannelId, messagingPlatformKey: $messagingPlatformKey, name: $name, profilePictureUrl: $profilePictureUrl, contactPictureUrl: $contactPictureUrl, contactEmail: $contactEmail, contactName: $contactName, contactMobilePhone: $contactMobilePhone, userFullPhotoUrl: $userFullPhotoUrl)';
  }
}

/// @nodoc
abstract mixin class $MessagingEndUserCopyWith<$Res> {
  factory $MessagingEndUserCopyWith(
          MessagingEndUser value, $Res Function(MessagingEndUser) _then) =
      _$MessagingEndUserCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId? contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId? messagingChannelId,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'ProfilePictureUrl') String? profilePictureUrl,
      @Deprecated('legacy') String? contactPictureUrl,
      @Deprecated('legacy') String? contactEmail,
      @Deprecated('legacy') String? contactName,
      @Deprecated('legacy') String? contactMobilePhone,
      @Deprecated('legacy') String? userFullPhotoUrl});

  $SfIdCopyWith<$Res> get id;
  $SfIdCopyWith<$Res>? get contactId;
  $SfIdCopyWith<$Res>? get messagingChannelId;
}

/// @nodoc
class _$MessagingEndUserCopyWithImpl<$Res>
    implements $MessagingEndUserCopyWith<$Res> {
  _$MessagingEndUserCopyWithImpl(this._self, this._then);

  final MessagingEndUser _self;
  final $Res Function(MessagingEndUser) _then;

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? contactId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingPlatformKey = freezed,
    Object? name = freezed,
    Object? profilePictureUrl = freezed,
    Object? contactPictureUrl = freezed,
    Object? contactEmail = freezed,
    Object? contactName = freezed,
    Object? contactMobilePhone = freezed,
    Object? userFullPhotoUrl = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      contactId: freezed == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingChannelId: freezed == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _self.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _self.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactPictureUrl: freezed == contactPictureUrl
          ? _self.contactPictureUrl
          : contactPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactEmail: freezed == contactEmail
          ? _self.contactEmail
          : contactEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      contactName: freezed == contactName
          ? _self.contactName
          : contactName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactMobilePhone: freezed == contactMobilePhone
          ? _self.contactMobilePhone
          : contactMobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      userFullPhotoUrl: freezed == userFullPhotoUrl
          ? _self.userFullPhotoUrl
          : userFullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get contactId {
    if (_self.contactId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.contactId!, (value) {
      return _then(_self.copyWith(contactId: value));
    });
  }

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingChannelId {
    if (_self.messagingChannelId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingChannelId!, (value) {
      return _then(_self.copyWith(messagingChannelId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingEndUser].
extension MessagingEndUserPatterns on MessagingEndUser {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingEndUser value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUser() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingEndUser value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUser():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingEndUser value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUser() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
            @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId? contactId,
            @JsonKey(name: 'MessagingChannelId')
            @ParseSfIdConverter()
            SfId? messagingChannelId,
            @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'ProfilePictureUrl') String? profilePictureUrl,
            @Deprecated('legacy') String? contactPictureUrl,
            @Deprecated('legacy') String? contactEmail,
            @Deprecated('legacy') String? contactName,
            @Deprecated('legacy') String? contactMobilePhone,
            @Deprecated('legacy') String? userFullPhotoUrl)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUser() when $default != null:
        return $default(
            _that.id,
            _that.contactId,
            _that.messagingChannelId,
            _that.messagingPlatformKey,
            _that.name,
            _that.profilePictureUrl,
            _that.contactPictureUrl,
            _that.contactEmail,
            _that.contactName,
            _that.contactMobilePhone,
            _that.userFullPhotoUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
            @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId? contactId,
            @JsonKey(name: 'MessagingChannelId')
            @ParseSfIdConverter()
            SfId? messagingChannelId,
            @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'ProfilePictureUrl') String? profilePictureUrl,
            @Deprecated('legacy') String? contactPictureUrl,
            @Deprecated('legacy') String? contactEmail,
            @Deprecated('legacy') String? contactName,
            @Deprecated('legacy') String? contactMobilePhone,
            @Deprecated('legacy') String? userFullPhotoUrl)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUser():
        return $default(
            _that.id,
            _that.contactId,
            _that.messagingChannelId,
            _that.messagingPlatformKey,
            _that.name,
            _that.profilePictureUrl,
            _that.contactPictureUrl,
            _that.contactEmail,
            _that.contactName,
            _that.contactMobilePhone,
            _that.userFullPhotoUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
            @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId? contactId,
            @JsonKey(name: 'MessagingChannelId')
            @ParseSfIdConverter()
            SfId? messagingChannelId,
            @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'ProfilePictureUrl') String? profilePictureUrl,
            @Deprecated('legacy') String? contactPictureUrl,
            @Deprecated('legacy') String? contactEmail,
            @Deprecated('legacy') String? contactName,
            @Deprecated('legacy') String? contactMobilePhone,
            @Deprecated('legacy') String? userFullPhotoUrl)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUser() when $default != null:
        return $default(
            _that.id,
            _that.contactId,
            _that.messagingChannelId,
            _that.messagingPlatformKey,
            _that.name,
            _that.profilePictureUrl,
            _that.contactPictureUrl,
            _that.contactEmail,
            _that.contactName,
            _that.contactMobilePhone,
            _that.userFullPhotoUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingEndUser extends MessagingEndUser {
  const _MessagingEndUser(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() required this.id,
      @JsonKey(name: 'ContactId') @ParseSfIdConverter() this.contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      this.messagingChannelId,
      @JsonKey(name: 'MessagingPlatformKey') this.messagingPlatformKey,
      @JsonKey(name: 'Name') this.name,
      @JsonKey(name: 'ProfilePictureUrl') this.profilePictureUrl,
      @Deprecated('legacy') this.contactPictureUrl,
      @Deprecated('legacy') this.contactEmail,
      @Deprecated('legacy') this.contactName,
      @Deprecated('legacy') this.contactMobilePhone,
      @Deprecated('legacy') this.userFullPhotoUrl})
      : super._();
  factory _MessagingEndUser.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserFromJson(json);

  @override
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  final SfId id;
// @JsonKey(name: 'AccountId') String?  accountId,
  @override
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  final SfId? contactId;
// @JsonKey(name: 'HasInitialResponseSent') bool?  hasInitialResponseSent,
// @JsonKey(name: 'IsFullyOptedIn') bool?  isFullyOptedIn,
// @JsonKey(name: 'IsOptedOut') bool?  isOptedOut,
// @JsonKey(name: 'IsoCountryCode') String?  isoCountryCode,
// @JsonKey(name: 'MessageType') String?  messageType,
  @override
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  final SfId? messagingChannelId;
// @JsonKey(name: 'MessagingConsentStatus') String?  messagingConsentStatus,
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  final String? messagingPlatformKey;
  @override
  @JsonKey(name: 'Name')
  final String? name;
// @JsonKey(name: 'OwnerId') String?  ownerId,
  @override
  @JsonKey(name: 'ProfilePictureUrl')
  final String? profilePictureUrl;

  /// items below are from the 'contact' object but were previously combined with this as such, these are added to support legacy code
  @override
  @Deprecated('legacy')
  final String? contactPictureUrl;
  @override
  @Deprecated('legacy')
  final String? contactEmail;
  @override
  @Deprecated('legacy')
  final String? contactName;
  @override
  @Deprecated('legacy')
  final String? contactMobilePhone;
  @override
  @Deprecated('legacy')
  final String? userFullPhotoUrl;

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingEndUserCopyWith<_MessagingEndUser> get copyWith =>
      __$MessagingEndUserCopyWithImpl<_MessagingEndUser>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingEndUserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingEndUser &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.contactPictureUrl, contactPictureUrl) ||
                other.contactPictureUrl == contactPictureUrl) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail) &&
            (identical(other.contactName, contactName) ||
                other.contactName == contactName) &&
            (identical(other.contactMobilePhone, contactMobilePhone) ||
                other.contactMobilePhone == contactMobilePhone) &&
            (identical(other.userFullPhotoUrl, userFullPhotoUrl) ||
                other.userFullPhotoUrl == userFullPhotoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      contactId,
      messagingChannelId,
      messagingPlatformKey,
      name,
      profilePictureUrl,
      contactPictureUrl,
      contactEmail,
      contactName,
      contactMobilePhone,
      userFullPhotoUrl);

  @override
  String toString() {
    return 'MessagingEndUser(id: $id, contactId: $contactId, messagingChannelId: $messagingChannelId, messagingPlatformKey: $messagingPlatformKey, name: $name, profilePictureUrl: $profilePictureUrl, contactPictureUrl: $contactPictureUrl, contactEmail: $contactEmail, contactName: $contactName, contactMobilePhone: $contactMobilePhone, userFullPhotoUrl: $userFullPhotoUrl)';
  }
}

/// @nodoc
abstract mixin class _$MessagingEndUserCopyWith<$Res>
    implements $MessagingEndUserCopyWith<$Res> {
  factory _$MessagingEndUserCopyWith(
          _MessagingEndUser value, $Res Function(_MessagingEndUser) _then) =
      __$MessagingEndUserCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId? contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId? messagingChannelId,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'ProfilePictureUrl') String? profilePictureUrl,
      @Deprecated('legacy') String? contactPictureUrl,
      @Deprecated('legacy') String? contactEmail,
      @Deprecated('legacy') String? contactName,
      @Deprecated('legacy') String? contactMobilePhone,
      @Deprecated('legacy') String? userFullPhotoUrl});

  @override
  $SfIdCopyWith<$Res> get id;
  @override
  $SfIdCopyWith<$Res>? get contactId;
  @override
  $SfIdCopyWith<$Res>? get messagingChannelId;
}

/// @nodoc
class __$MessagingEndUserCopyWithImpl<$Res>
    implements _$MessagingEndUserCopyWith<$Res> {
  __$MessagingEndUserCopyWithImpl(this._self, this._then);

  final _MessagingEndUser _self;
  final $Res Function(_MessagingEndUser) _then;

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? contactId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingPlatformKey = freezed,
    Object? name = freezed,
    Object? profilePictureUrl = freezed,
    Object? contactPictureUrl = freezed,
    Object? contactEmail = freezed,
    Object? contactName = freezed,
    Object? contactMobilePhone = freezed,
    Object? userFullPhotoUrl = freezed,
  }) {
    return _then(_MessagingEndUser(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      contactId: freezed == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingChannelId: freezed == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _self.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _self.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactPictureUrl: freezed == contactPictureUrl
          ? _self.contactPictureUrl
          : contactPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactEmail: freezed == contactEmail
          ? _self.contactEmail
          : contactEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      contactName: freezed == contactName
          ? _self.contactName
          : contactName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactMobilePhone: freezed == contactMobilePhone
          ? _self.contactMobilePhone
          : contactMobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      userFullPhotoUrl: freezed == userFullPhotoUrl
          ? _self.userFullPhotoUrl
          : userFullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get contactId {
    if (_self.contactId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.contactId!, (value) {
      return _then(_self.copyWith(contactId: value));
    });
  }

  /// Create a copy of MessagingEndUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingChannelId {
    if (_self.messagingChannelId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingChannelId!, (value) {
      return _then(_self.copyWith(messagingChannelId: value));
    });
  }
}

// dart format on
