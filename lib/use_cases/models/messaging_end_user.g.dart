// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_end_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingEndUser _$MessagingEndUserFromJson(Map json) => $checkedCreate(
      '_MessagingEndUser',
      json,
      ($checkedConvert) {
        final val = _MessagingEndUser(
          id: $checkedConvert(
              'Id', (v) => const ParseSfIdConverter().from<PERSON>son(v)),
          contactId: $checkedConvert(
              'ContactId', (v) => const ParseSfIdConverter().from<PERSON>son(v)),
          messagingChannelId: $checkedConvert('MessagingChannelId',
              (v) => const ParseSfIdConverter().fromJson(v)),
          messagingPlatformKey:
              $checkedConvert('MessagingPlatformKey', (v) => v as String?),
          name: $checkedConvert('Name', (v) => v as String?),
          profilePictureUrl:
              $checkedConvert('ProfilePictureUrl', (v) => v as String?),
          contactPictureUrl:
              $checkedConvert('contactPictureUrl', (v) => v as String?),
          contactEmail: $checkedConvert('contactEmail', (v) => v as String?),
          contactName: $checkedConvert('contactName', (v) => v as String?),
          contactMobilePhone:
              $checkedConvert('contactMobilePhone', (v) => v as String?),
          userFullPhotoUrl:
              $checkedConvert('userFullPhotoUrl', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'id': 'Id',
        'contactId': 'ContactId',
        'messagingChannelId': 'MessagingChannelId',
        'messagingPlatformKey': 'MessagingPlatformKey',
        'name': 'Name',
        'profilePictureUrl': 'ProfilePictureUrl'
      },
    );

Map<String, dynamic> _$MessagingEndUserToJson(_MessagingEndUser instance) =>
    <String, dynamic>{
      if (const ParseSfIdConverter().toJson(instance.id) case final value?)
        'Id': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.contactId, const ParseSfIdConverter().toJson)
          case final value?)
        'ContactId': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.messagingChannelId, const ParseSfIdConverter().toJson)
          case final value?)
        'MessagingChannelId': value,
      if (instance.messagingPlatformKey case final value?)
        'MessagingPlatformKey': value,
      if (instance.name case final value?) 'Name': value,
      if (instance.profilePictureUrl case final value?)
        'ProfilePictureUrl': value,
      if (instance.contactPictureUrl case final value?)
        'contactPictureUrl': value,
      if (instance.contactEmail case final value?) 'contactEmail': value,
      if (instance.contactName case final value?) 'contactName': value,
      if (instance.contactMobilePhone case final value?)
        'contactMobilePhone': value,
      if (instance.userFullPhotoUrl case final value?)
        'userFullPhotoUrl': value,
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
