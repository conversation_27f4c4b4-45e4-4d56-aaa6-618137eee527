// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_messaging_session.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OutboundMessagingSession {
  String get messagingEndUserId;
  String get messagingChannelId;

  /// Create a copy of OutboundMessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OutboundMessagingSessionCopyWith<OutboundMessagingSession> get copyWith =>
      _$OutboundMessagingSessionCopyWithImpl<OutboundMessagingSession>(
          this as OutboundMessagingSession, _$identity);

  /// Serializes this OutboundMessagingSession to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OutboundMessagingSession &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, messagingEndUserId, messagingChannelId);

  @override
  String toString() {
    return 'OutboundMessagingSession(messagingEndUserId: $messagingEndUserId, messagingChannelId: $messagingChannelId)';
  }
}

/// @nodoc
abstract mixin class $OutboundMessagingSessionCopyWith<$Res> {
  factory $OutboundMessagingSessionCopyWith(OutboundMessagingSession value,
          $Res Function(OutboundMessagingSession) _then) =
      _$OutboundMessagingSessionCopyWithImpl;
  @useResult
  $Res call({String messagingEndUserId, String messagingChannelId});
}

/// @nodoc
class _$OutboundMessagingSessionCopyWithImpl<$Res>
    implements $OutboundMessagingSessionCopyWith<$Res> {
  _$OutboundMessagingSessionCopyWithImpl(this._self, this._then);

  final OutboundMessagingSession _self;
  final $Res Function(OutboundMessagingSession) _then;

  /// Create a copy of OutboundMessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingEndUserId = null,
    Object? messagingChannelId = null,
  }) {
    return _then(_self.copyWith(
      messagingEndUserId: null == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingChannelId: null == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [OutboundMessagingSession].
extension OutboundMessagingSessionPatterns on OutboundMessagingSession {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OutboundMessagingSession value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagingSession() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OutboundMessagingSession value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagingSession():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OutboundMessagingSession value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagingSession() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String messagingEndUserId, String messagingChannelId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagingSession() when $default != null:
        return $default(_that.messagingEndUserId, _that.messagingChannelId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String messagingEndUserId, String messagingChannelId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagingSession():
        return $default(_that.messagingEndUserId, _that.messagingChannelId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String messagingEndUserId, String messagingChannelId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagingSession() when $default != null:
        return $default(_that.messagingEndUserId, _that.messagingChannelId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _OutboundMessagingSession implements OutboundMessagingSession {
  const _OutboundMessagingSession(
      {required this.messagingEndUserId, required this.messagingChannelId});
  factory _OutboundMessagingSession.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessagingSessionFromJson(json);

  @override
  final String messagingEndUserId;
  @override
  final String messagingChannelId;

  /// Create a copy of OutboundMessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OutboundMessagingSessionCopyWith<_OutboundMessagingSession> get copyWith =>
      __$OutboundMessagingSessionCopyWithImpl<_OutboundMessagingSession>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OutboundMessagingSessionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OutboundMessagingSession &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, messagingEndUserId, messagingChannelId);

  @override
  String toString() {
    return 'OutboundMessagingSession(messagingEndUserId: $messagingEndUserId, messagingChannelId: $messagingChannelId)';
  }
}

/// @nodoc
abstract mixin class _$OutboundMessagingSessionCopyWith<$Res>
    implements $OutboundMessagingSessionCopyWith<$Res> {
  factory _$OutboundMessagingSessionCopyWith(_OutboundMessagingSession value,
          $Res Function(_OutboundMessagingSession) _then) =
      __$OutboundMessagingSessionCopyWithImpl;
  @override
  @useResult
  $Res call({String messagingEndUserId, String messagingChannelId});
}

/// @nodoc
class __$OutboundMessagingSessionCopyWithImpl<$Res>
    implements _$OutboundMessagingSessionCopyWith<$Res> {
  __$OutboundMessagingSessionCopyWithImpl(this._self, this._then);

  final _OutboundMessagingSession _self;
  final $Res Function(_OutboundMessagingSession) _then;

  /// Create a copy of OutboundMessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messagingEndUserId = null,
    Object? messagingChannelId = null,
  }) {
    return _then(_OutboundMessagingSession(
      messagingEndUserId: null == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingChannelId: null == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
