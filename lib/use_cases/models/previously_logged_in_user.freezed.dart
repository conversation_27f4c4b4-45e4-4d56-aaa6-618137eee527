// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'previously_logged_in_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PreviouslyLoggedInUser {
  String? get userId;
  String? get orgId;
  String? get presenceId;

  /// Create a copy of PreviouslyLoggedInUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PreviouslyLoggedInUserCopyWith<PreviouslyLoggedInUser> get copyWith =>
      _$PreviouslyLoggedInUserCopyWithImpl<PreviouslyLoggedInUser>(
          this as PreviouslyLoggedInUser, _$identity);

  /// Serializes this PreviouslyLoggedInUser to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PreviouslyLoggedInUser &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.presenceId, presenceId) ||
                other.presenceId == presenceId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, orgId, presenceId);

  @override
  String toString() {
    return 'PreviouslyLoggedInUser(userId: $userId, orgId: $orgId, presenceId: $presenceId)';
  }
}

/// @nodoc
abstract mixin class $PreviouslyLoggedInUserCopyWith<$Res> {
  factory $PreviouslyLoggedInUserCopyWith(PreviouslyLoggedInUser value,
          $Res Function(PreviouslyLoggedInUser) _then) =
      _$PreviouslyLoggedInUserCopyWithImpl;
  @useResult
  $Res call({String? userId, String? orgId, String? presenceId});
}

/// @nodoc
class _$PreviouslyLoggedInUserCopyWithImpl<$Res>
    implements $PreviouslyLoggedInUserCopyWith<$Res> {
  _$PreviouslyLoggedInUserCopyWithImpl(this._self, this._then);

  final PreviouslyLoggedInUser _self;
  final $Res Function(PreviouslyLoggedInUser) _then;

  /// Create a copy of PreviouslyLoggedInUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? orgId = freezed,
    Object? presenceId = freezed,
  }) {
    return _then(_self.copyWith(
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      presenceId: freezed == presenceId
          ? _self.presenceId
          : presenceId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [PreviouslyLoggedInUser].
extension PreviouslyLoggedInUserPatterns on PreviouslyLoggedInUser {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PreviouslyLoggedInUser value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PreviouslyLoggedInUser() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PreviouslyLoggedInUser value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PreviouslyLoggedInUser():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PreviouslyLoggedInUser value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PreviouslyLoggedInUser() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? userId, String? orgId, String? presenceId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PreviouslyLoggedInUser() when $default != null:
        return $default(_that.userId, _that.orgId, _that.presenceId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? userId, String? orgId, String? presenceId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PreviouslyLoggedInUser():
        return $default(_that.userId, _that.orgId, _that.presenceId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? userId, String? orgId, String? presenceId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PreviouslyLoggedInUser() when $default != null:
        return $default(_that.userId, _that.orgId, _that.presenceId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PreviouslyLoggedInUser extends PreviouslyLoggedInUser {
  const _PreviouslyLoggedInUser({this.userId, this.orgId, this.presenceId})
      : super._();
  factory _PreviouslyLoggedInUser.fromJson(Map<String, dynamic> json) =>
      _$PreviouslyLoggedInUserFromJson(json);

  @override
  final String? userId;
  @override
  final String? orgId;
  @override
  final String? presenceId;

  /// Create a copy of PreviouslyLoggedInUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PreviouslyLoggedInUserCopyWith<_PreviouslyLoggedInUser> get copyWith =>
      __$PreviouslyLoggedInUserCopyWithImpl<_PreviouslyLoggedInUser>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PreviouslyLoggedInUserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PreviouslyLoggedInUser &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.presenceId, presenceId) ||
                other.presenceId == presenceId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, orgId, presenceId);

  @override
  String toString() {
    return 'PreviouslyLoggedInUser(userId: $userId, orgId: $orgId, presenceId: $presenceId)';
  }
}

/// @nodoc
abstract mixin class _$PreviouslyLoggedInUserCopyWith<$Res>
    implements $PreviouslyLoggedInUserCopyWith<$Res> {
  factory _$PreviouslyLoggedInUserCopyWith(_PreviouslyLoggedInUser value,
          $Res Function(_PreviouslyLoggedInUser) _then) =
      __$PreviouslyLoggedInUserCopyWithImpl;
  @override
  @useResult
  $Res call({String? userId, String? orgId, String? presenceId});
}

/// @nodoc
class __$PreviouslyLoggedInUserCopyWithImpl<$Res>
    implements _$PreviouslyLoggedInUserCopyWith<$Res> {
  __$PreviouslyLoggedInUserCopyWithImpl(this._self, this._then);

  final _PreviouslyLoggedInUser _self;
  final $Res Function(_PreviouslyLoggedInUser) _then;

  /// Create a copy of PreviouslyLoggedInUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = freezed,
    Object? orgId = freezed,
    Object? presenceId = freezed,
  }) {
    return _then(_PreviouslyLoggedInUser(
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      presenceId: freezed == presenceId
          ? _self.presenceId
          : presenceId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
