// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'previously_logged_in_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PreviouslyLoggedInUser _$PreviouslyLoggedInUserFromJson(Map json) =>
    $checkedCreate(
      '_PreviouslyLoggedInUser',
      json,
      ($checkedConvert) {
        final val = _PreviouslyLoggedInUser(
          userId: $checkedConvert('userId', (v) => v as String?),
          orgId: $checkedConvert('orgId', (v) => v as String?),
          presenceId: $checkedConvert('presenceId', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$PreviouslyLoggedInUserToJson(
        _PreviouslyLoggedInUser instance) =>
    <String, dynamic>{
      if (instance.userId case final value?) 'userId': value,
      if (instance.orgId case final value?) 'orgId': value,
      if (instance.presenceId case final value?) 'presenceId': value,
    };
