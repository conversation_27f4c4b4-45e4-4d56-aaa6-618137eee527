// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_environment.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesforceEnvironment {
  @JsonKey(
      fromJson: _sfEnvironmentTypeFromJson, toJson: _sfEnvironmentTypeToJson)
  SalesforceEnvironmentType get type;
  String? get customDomainBase;

  /// Create a copy of SalesforceEnvironment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SalesforceEnvironmentCopyWith<SalesforceEnvironment> get copyWith =>
      _$SalesforceEnvironmentCopyWithImpl<SalesforceEnvironment>(
          this as SalesforceEnvironment, _$identity);

  /// Serializes this SalesforceEnvironment to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SalesforceEnvironment &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.customDomainBase, customDomainBase) ||
                other.customDomainBase == customDomainBase));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, customDomainBase);

  @override
  String toString() {
    return 'SalesforceEnvironment(type: $type, customDomainBase: $customDomainBase)';
  }
}

/// @nodoc
abstract mixin class $SalesforceEnvironmentCopyWith<$Res> {
  factory $SalesforceEnvironmentCopyWith(SalesforceEnvironment value,
          $Res Function(SalesforceEnvironment) _then) =
      _$SalesforceEnvironmentCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(
          fromJson: _sfEnvironmentTypeFromJson,
          toJson: _sfEnvironmentTypeToJson)
      SalesforceEnvironmentType type,
      String? customDomainBase});
}

/// @nodoc
class _$SalesforceEnvironmentCopyWithImpl<$Res>
    implements $SalesforceEnvironmentCopyWith<$Res> {
  _$SalesforceEnvironmentCopyWithImpl(this._self, this._then);

  final SalesforceEnvironment _self;
  final $Res Function(SalesforceEnvironment) _then;

  /// Create a copy of SalesforceEnvironment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? customDomainBase = freezed,
  }) {
    return _then(_self.copyWith(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironmentType,
      customDomainBase: freezed == customDomainBase
          ? _self.customDomainBase
          : customDomainBase // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SalesforceEnvironment].
extension SalesforceEnvironmentPatterns on SalesforceEnvironment {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SalesforceEnvironment value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceEnvironment() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SalesforceEnvironment value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceEnvironment():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SalesforceEnvironment value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceEnvironment() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(
                fromJson: _sfEnvironmentTypeFromJson,
                toJson: _sfEnvironmentTypeToJson)
            SalesforceEnvironmentType type,
            String? customDomainBase)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceEnvironment() when $default != null:
        return $default(_that.type, _that.customDomainBase);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(
                fromJson: _sfEnvironmentTypeFromJson,
                toJson: _sfEnvironmentTypeToJson)
            SalesforceEnvironmentType type,
            String? customDomainBase)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceEnvironment():
        return $default(_that.type, _that.customDomainBase);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(
                fromJson: _sfEnvironmentTypeFromJson,
                toJson: _sfEnvironmentTypeToJson)
            SalesforceEnvironmentType type,
            String? customDomainBase)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceEnvironment() when $default != null:
        return $default(_that.type, _that.customDomainBase);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SalesforceEnvironment extends SalesforceEnvironment {
  const _SalesforceEnvironment(
      {@JsonKey(
          fromJson: _sfEnvironmentTypeFromJson,
          toJson: _sfEnvironmentTypeToJson)
      this.type = SalesforceEnvironmentType.production,
      this.customDomainBase})
      : super._();
  factory _SalesforceEnvironment.fromJson(Map<String, dynamic> json) =>
      _$SalesforceEnvironmentFromJson(json);

  @override
  @JsonKey(
      fromJson: _sfEnvironmentTypeFromJson, toJson: _sfEnvironmentTypeToJson)
  final SalesforceEnvironmentType type;
  @override
  final String? customDomainBase;

  /// Create a copy of SalesforceEnvironment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SalesforceEnvironmentCopyWith<_SalesforceEnvironment> get copyWith =>
      __$SalesforceEnvironmentCopyWithImpl<_SalesforceEnvironment>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SalesforceEnvironmentToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SalesforceEnvironment &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.customDomainBase, customDomainBase) ||
                other.customDomainBase == customDomainBase));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, customDomainBase);

  @override
  String toString() {
    return 'SalesforceEnvironment(type: $type, customDomainBase: $customDomainBase)';
  }
}

/// @nodoc
abstract mixin class _$SalesforceEnvironmentCopyWith<$Res>
    implements $SalesforceEnvironmentCopyWith<$Res> {
  factory _$SalesforceEnvironmentCopyWith(_SalesforceEnvironment value,
          $Res Function(_SalesforceEnvironment) _then) =
      __$SalesforceEnvironmentCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(
          fromJson: _sfEnvironmentTypeFromJson,
          toJson: _sfEnvironmentTypeToJson)
      SalesforceEnvironmentType type,
      String? customDomainBase});
}

/// @nodoc
class __$SalesforceEnvironmentCopyWithImpl<$Res>
    implements _$SalesforceEnvironmentCopyWith<$Res> {
  __$SalesforceEnvironmentCopyWithImpl(this._self, this._then);

  final _SalesforceEnvironment _self;
  final $Res Function(_SalesforceEnvironment) _then;

  /// Create a copy of SalesforceEnvironment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? type = null,
    Object? customDomainBase = freezed,
  }) {
    return _then(_SalesforceEnvironment(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironmentType,
      customDomainBase: freezed == customDomainBase
          ? _self.customDomainBase
          : customDomainBase // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
