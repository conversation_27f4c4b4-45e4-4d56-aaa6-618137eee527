// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salesforce_environment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SalesforceEnvironment _$SalesforceEnvironmentFromJson(Map json) =>
    $checkedCreate(
      '_SalesforceEnvironment',
      json,
      ($checkedConvert) {
        final val = _SalesforceEnvironment(
          type: $checkedConvert(
              'type',
              (v) => v == null
                  ? SalesforceEnvironmentType.production
                  : _sfEnvironmentTypeFromJson(v as String)),
          customDomainBase:
              $checkedConvert('customDomainBase', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$SalesforceEnvironmentToJson(
        _SalesforceEnvironment instance) =>
    <String, dynamic>{
      'type': _sfEnvironmentTypeToJson(instance.type),
      if (instance.customDomainBase case final value?)
        'customDomainBase': value,
    };
