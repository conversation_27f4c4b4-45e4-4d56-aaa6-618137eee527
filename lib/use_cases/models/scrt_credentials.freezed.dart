// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'scrt_credentials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ScrtCredentials {
  String? get scrtAccessToken;
  String? get scrtHost;

  /// Create a copy of ScrtCredentials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ScrtCredentialsCopyWith<ScrtCredentials> get copyWith =>
      _$ScrtCredentialsCopyWithImpl<ScrtCredentials>(
          this as ScrtCredentials, _$identity);

  /// Serializes this ScrtCredentials to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ScrtCredentials &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken) &&
            (identical(other.scrtHost, scrtHost) ||
                other.scrtHost == scrtHost));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, scrtAccessToken, scrtHost);

  @override
  String toString() {
    return 'ScrtCredentials(scrtAccessToken: $scrtAccessToken, scrtHost: $scrtHost)';
  }
}

/// @nodoc
abstract mixin class $ScrtCredentialsCopyWith<$Res> {
  factory $ScrtCredentialsCopyWith(
          ScrtCredentials value, $Res Function(ScrtCredentials) _then) =
      _$ScrtCredentialsCopyWithImpl;
  @useResult
  $Res call({String? scrtAccessToken, String? scrtHost});
}

/// @nodoc
class _$ScrtCredentialsCopyWithImpl<$Res>
    implements $ScrtCredentialsCopyWith<$Res> {
  _$ScrtCredentialsCopyWithImpl(this._self, this._then);

  final ScrtCredentials _self;
  final $Res Function(ScrtCredentials) _then;

  /// Create a copy of ScrtCredentials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
  }) {
    return _then(_self.copyWith(
      scrtAccessToken: freezed == scrtAccessToken
          ? _self.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _self.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ScrtCredentials].
extension ScrtCredentialsPatterns on ScrtCredentials {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ScrtCredentials value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ScrtCredentials() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ScrtCredentials value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ScrtCredentials():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ScrtCredentials value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ScrtCredentials() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? scrtAccessToken, String? scrtHost)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ScrtCredentials() when $default != null:
        return $default(_that.scrtAccessToken, _that.scrtHost);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? scrtAccessToken, String? scrtHost) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ScrtCredentials():
        return $default(_that.scrtAccessToken, _that.scrtHost);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? scrtAccessToken, String? scrtHost)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ScrtCredentials() when $default != null:
        return $default(_that.scrtAccessToken, _that.scrtHost);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ScrtCredentials extends ScrtCredentials {
  const _ScrtCredentials({this.scrtAccessToken, this.scrtHost}) : super._();
  factory _ScrtCredentials.fromJson(Map<String, dynamic> json) =>
      _$ScrtCredentialsFromJson(json);

  @override
  final String? scrtAccessToken;
  @override
  final String? scrtHost;

  /// Create a copy of ScrtCredentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ScrtCredentialsCopyWith<_ScrtCredentials> get copyWith =>
      __$ScrtCredentialsCopyWithImpl<_ScrtCredentials>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ScrtCredentialsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ScrtCredentials &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken) &&
            (identical(other.scrtHost, scrtHost) ||
                other.scrtHost == scrtHost));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, scrtAccessToken, scrtHost);

  @override
  String toString() {
    return 'ScrtCredentials(scrtAccessToken: $scrtAccessToken, scrtHost: $scrtHost)';
  }
}

/// @nodoc
abstract mixin class _$ScrtCredentialsCopyWith<$Res>
    implements $ScrtCredentialsCopyWith<$Res> {
  factory _$ScrtCredentialsCopyWith(
          _ScrtCredentials value, $Res Function(_ScrtCredentials) _then) =
      __$ScrtCredentialsCopyWithImpl;
  @override
  @useResult
  $Res call({String? scrtAccessToken, String? scrtHost});
}

/// @nodoc
class __$ScrtCredentialsCopyWithImpl<$Res>
    implements _$ScrtCredentialsCopyWith<$Res> {
  __$ScrtCredentialsCopyWithImpl(this._self, this._then);

  final _ScrtCredentials _self;
  final $Res Function(_ScrtCredentials) _then;

  /// Create a copy of ScrtCredentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
  }) {
    return _then(_ScrtCredentials(
      scrtAccessToken: freezed == scrtAccessToken
          ? _self.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _self.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
