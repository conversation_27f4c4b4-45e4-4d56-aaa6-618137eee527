// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scrt_credentials.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ScrtCredentials _$ScrtCredentialsFromJson(Map json) => $checkedCreate(
      '_ScrtCredentials',
      json,
      ($checkedConvert) {
        final val = _ScrtCredentials(
          scrtAccessToken:
              $checkedConvert('scrtAccessToken', (v) => v as String?),
          scrtHost: $checkedConvert('scrtHost', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$ScrtCredentialsToJson(_ScrtCredentials instance) =>
    <String, dynamic>{
      if (instance.scrtAccessToken case final value?) 'scrtAccessToken': value,
      if (instance.scrtHost case final value?) 'scrtHost': value,
    };
