// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_capabilities.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkCapabilities {
  /// NOTE: removing this as it had issues*; shim service was just checking whether the conversation was enhanced (whether it had a 'ConversationIdentifier') [CRM, 10/3/2024]
  /// * issues if the work was available b/c of WorksOpened (upon login) or hard close & re-open
// @Default(false) bool canTransfer,
  dynamic get canRaiseFlag;

  /// Create a copy of WorkCapabilities
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkCapabilitiesCopyWith<WorkCapabilities> get copyWith =>
      _$WorkCapabilitiesCopyWithImpl<WorkCapabilities>(
          this as WorkCapabilities, _$identity);

  /// Serializes this WorkCapabilities to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkCapabilities &&
            const DeepCollectionEquality()
                .equals(other.canRaiseFlag, canRaiseFlag));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(canRaiseFlag));

  @override
  String toString() {
    return 'WorkCapabilities(canRaiseFlag: $canRaiseFlag)';
  }
}

/// @nodoc
abstract mixin class $WorkCapabilitiesCopyWith<$Res> {
  factory $WorkCapabilitiesCopyWith(
          WorkCapabilities value, $Res Function(WorkCapabilities) _then) =
      _$WorkCapabilitiesCopyWithImpl;
  @useResult
  $Res call({dynamic canRaiseFlag});
}

/// @nodoc
class _$WorkCapabilitiesCopyWithImpl<$Res>
    implements $WorkCapabilitiesCopyWith<$Res> {
  _$WorkCapabilitiesCopyWithImpl(this._self, this._then);

  final WorkCapabilities _self;
  final $Res Function(WorkCapabilities) _then;

  /// Create a copy of WorkCapabilities
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canRaiseFlag = freezed,
  }) {
    return _then(_self.copyWith(
      canRaiseFlag: freezed == canRaiseFlag
          ? _self.canRaiseFlag
          : canRaiseFlag // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// Adds pattern-matching-related methods to [WorkCapabilities].
extension WorkCapabilitiesPatterns on WorkCapabilities {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WorkCapabilities value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkCapabilities() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WorkCapabilities value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkCapabilities():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WorkCapabilities value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkCapabilities() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(dynamic canRaiseFlag)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkCapabilities() when $default != null:
        return $default(_that.canRaiseFlag);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(dynamic canRaiseFlag) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkCapabilities():
        return $default(_that.canRaiseFlag);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(dynamic canRaiseFlag)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkCapabilities() when $default != null:
        return $default(_that.canRaiseFlag);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WorkCapabilities extends WorkCapabilities {
  const _WorkCapabilities({this.canRaiseFlag = false}) : super._();
  factory _WorkCapabilities.fromJson(Map<String, dynamic> json) =>
      _$WorkCapabilitiesFromJson(json);

  /// NOTE: removing this as it had issues*; shim service was just checking whether the conversation was enhanced (whether it had a 'ConversationIdentifier') [CRM, 10/3/2024]
  /// * issues if the work was available b/c of WorksOpened (upon login) or hard close & re-open
// @Default(false) bool canTransfer,
  @override
  @JsonKey()
  final dynamic canRaiseFlag;

  /// Create a copy of WorkCapabilities
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkCapabilitiesCopyWith<_WorkCapabilities> get copyWith =>
      __$WorkCapabilitiesCopyWithImpl<_WorkCapabilities>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkCapabilitiesToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkCapabilities &&
            const DeepCollectionEquality()
                .equals(other.canRaiseFlag, canRaiseFlag));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(canRaiseFlag));

  @override
  String toString() {
    return 'WorkCapabilities(canRaiseFlag: $canRaiseFlag)';
  }
}

/// @nodoc
abstract mixin class _$WorkCapabilitiesCopyWith<$Res>
    implements $WorkCapabilitiesCopyWith<$Res> {
  factory _$WorkCapabilitiesCopyWith(
          _WorkCapabilities value, $Res Function(_WorkCapabilities) _then) =
      __$WorkCapabilitiesCopyWithImpl;
  @override
  @useResult
  $Res call({dynamic canRaiseFlag});
}

/// @nodoc
class __$WorkCapabilitiesCopyWithImpl<$Res>
    implements _$WorkCapabilitiesCopyWith<$Res> {
  __$WorkCapabilitiesCopyWithImpl(this._self, this._then);

  final _WorkCapabilities _self;
  final $Res Function(_WorkCapabilities) _then;

  /// Create a copy of WorkCapabilities
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? canRaiseFlag = freezed,
  }) {
    return _then(_WorkCapabilities(
      canRaiseFlag: freezed == canRaiseFlag
          ? _self.canRaiseFlag
          : canRaiseFlag // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

// dart format on
