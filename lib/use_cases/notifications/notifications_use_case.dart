import 'dart:async';
import 'dart:ui';

import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/notification_message.dart';
import 'package:x1440/api/dtos/notification_message_response.dart';
import 'package:x1440/api/dtos/unacknowledged_notifications_body.dart';
import 'package:x1440/api/dtos/unacknowledged_notifications_response.dart';
import 'package:x1440/frameworks/notifications/notifications_event.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart';
import 'package:x1440/repositories/message_queue/message_queue_repository.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/notifications/notifications_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';

/// Refactor Note: we have a separate Manager that does not use this UseCase. It has a lot of additional Notifications logic that should be moved here
class NotificationsUseCase {
  final NotificationsRepository _notificationsRepository;
  final AppLifeCycleRepository _appLifeCycleRepository;
  final MessageQueueRepository _messageQueueRepository;
  final LocalStorageRepository _localStorage;
  final SessionUseCase _sessionUseCase;
  final RemoteLogger _logger;

  NotificationsUseCase(
      this._notificationsRepository,
      this._appLifeCycleRepository,
      this._messageQueueRepository,
      this._localStorage,
      this._sessionUseCase,
      this._logger);

  Future<void> init() async {
    _appLifeCycleRepository.appLifeCycleStream.listen((event) async {
      GetIt.I<NotificationManager>().add(HandleAppOpenNotificationsEvent(event));
      // await _handleAppOpen(event);
    });
    // GetIt.I<NotificationManager>().add(HandleAppOpenNotificationsEvent(_appLifeCycleRepository.appLifecycleState ?? AppLifecycleState.resumed));
    // handleAppOpen(_appLifeCycleRepository.appLifecycleState);
  }

  Future<void> handleAppOpen(AppLifecycleState? event) async {
    if (event == AppLifecycleState.resumed) {
      bool isLoggedIn = await _localStorage
          .getCredentials()
          .then((value) => value.isLoggedIn);
      bool isPresenceActive =
          isLoggedIn && GetIt.I<PresenceManager>().state.currentStatus.isOnline;
      if (!isPresenceActive) {
        try {
          // Check if AuthBloc is registered before using it
          if (GetIt.I.isRegistered<AuthBloc>()) {
            GetIt.I<AuthBloc>().add(AttemptAutoLoginEvent());
          } else {
            _logger.warn('AuthBloc not registered yet, skipping auto login');
          }
        } catch (e) {
          _logger.warn('Error accessing AuthBloc: $e');
        }
        return;
      }
      getUnacknowledgedNotifications();
    }
  }

  Future<NotificationMessageResponse> getNotificationMessage(
      String token) async {
    final response =
        await _notificationsRepository.getNotificationMessage(token);
    if (response is Error) {
      throw ApiException((response as Error).error);
    } else {
      Success result = response as Success;
      NotificationMessageResponse payload = result.data;

      return payload;
    }
  }

  Completer<UnacknowledgedNotificationsResponse>?
      _unacknowledgedNotificationsCompleter;

  Future<UnacknowledgedNotificationsResponse> getUnacknowledgedNotifications(
      [UnacknowledgedNotificationsBody? body]) async {
    if (_unacknowledgedNotificationsCompleter != null) {
      if (_unacknowledgedNotificationsCompleter!.isCompleted) {
        _unacknowledgedNotificationsCompleter = null;
        return getUnacknowledgedNotifications(body);
      }
      return _unacknowledgedNotificationsCompleter!.future;
    }

    _unacknowledgedNotificationsCompleter =
        Completer<UnacknowledgedNotificationsResponse>();

    final response =
        await _notificationsRepository.getUnacknowledgedNotifications(body);
    if (response is Error) {
      _logger.warn(
          'Failed to get unacknowledged notifications - ${(response as Error).error}');
      _unacknowledgedNotificationsCompleter!
          .completeError(ApiException((response as Error).error));
      _unacknowledgedNotificationsCompleter = null;
      return const UnacknowledgedNotificationsResponse();
      // throw ApiException((response as Error).error);
    } else {
      Success result = response as Success;
      UnacknowledgedNotificationsResponse payload = result.data;

      if (payload.sessionActive == false) {
        _unacknowledgedNotificationsCompleter!.complete(payload);
        _unacknowledgedNotificationsCompleter = null;
        try {
          // Check if AuthBloc is registered before using it
          if (GetIt.I.isRegistered<AuthBloc>()) {
            GetIt.I<AuthBloc>().add(SessionExpiredEvent());
          } else {
            _logger.warn(
                'AuthBloc not registered yet, skipping session expired event');
          }
        } catch (e) {
          _logger.warn('Error accessing AuthBloc: $e');
        }
        return payload;
      }

      // if (payload.sessionId != null) {
      //   String? currentSessionId = await _presenceManager.getCurrentSessionId();
      //   if (currentSessionId != null && currentSessionId != payload.sessionId) {
      //     _unacknowledgedNotificationsCompleter!.complete(payload);
      //     _unacknowledgedNotificationsCompleter = null;
      //     return payload;
      //   }
      // }

      final creds = await _localStorage.getCredentials();
      payload.notifications?.forEach((NotificationMessage notification) {
        /// check that NotificationMessage is in same session as currently active
        if (notification.sessionId != creds.sessionId) {
          _logger.info(
              'NotificationMessage is in different session than currently active; currentSessionId: ${creds.sessionId}; notificationSessionId: ${notification.sessionId}');
          return;
        }
        _messageQueueRepository.addMessageToReceiveQueue(
            QueueReceiveMessage.fromNotificationMessage(notification));
      });
      await handleSessionGone(payload);
      _unacknowledgedNotificationsCompleter!.complete(payload);
      _unacknowledgedNotificationsCompleter = null;
      return payload;
    }
  }

  Future<void> handleSessionGone(
      UnacknowledgedNotificationsResponse payload) async {
    if (payload.sessionActive != null && payload.sessionActive == false) {
      _logger.info('session is gone, starting new session');
      await _sessionUseCase.startSession();
    }
  }
}
