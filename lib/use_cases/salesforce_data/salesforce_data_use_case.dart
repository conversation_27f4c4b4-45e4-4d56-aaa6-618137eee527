import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:file_saver/file_saver.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/salesforce/salesforce_api.dart';
import 'package:x1440/models/conversation_entry_model.dart';
import 'package:x1440/repositories/salesforce_data_repository/salesforce_data_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/utils/string_utils.dart';

class SalesforceDataUseCase {
  final RemoteLogger _logger;
  final SalesforceDataRepository _salesforceDataRepository;
  final LocalStorageRepository _localStorageRepository;

  SalesforceDataUseCase(this._logger, this._salesforceDataRepository,
      this._localStorageRepository);

  Future<MapEntry<String, File>?> saveFile(File file) =>
      _salesforceDataRepository.saveFile(file);

  Future<void> saveContentVersionToLocalFile(
      String contentVersionId, String fileName, MimeType mimeType) async {
    List<String> fileParts = fileName.toString().split('.');
    if (fileParts.length < 2) {
      _logger.error('fileParts.length < 2: $fileParts');
      return;
    }

    Credentials credentials = await _localStorageRepository.getCredentials();

    if (credentials.instanceUrl == null) throw Exception('saveContentVersionToLocalFile failure: instanceUrl is null');
    final sfApi =
        SalesforceApi(GetIt.instance<Dio>(), baseUrl: credentials.instanceUrl!);

    final response = await sfApi.contentVersionVersionData(contentVersionId);

    Uint8List encodedBytes = Uint8List.fromList(response.data);

    // Updated to match newer FileSaver API - now requires fileExtension parameter
    FileSaver.instance.saveAs(
        name: fileName.replaceAll('.${fileParts.last}', ''),
        fileExtension: fileParts.last, // New parameter replacing 'ext'
        mimeType: mimeType,
        bytes: encodedBytes);
  }

  Future<ConversationEntryAttachment> getConversationEntryAttachmentFromContentVersionId(String contentVersionId) async {
    Credentials credentials = await _localStorageRepository.getCredentials();

    if (credentials.instanceUrl == null) throw Exception('getConversationEntryAttachmentFromContentVersionId failure: instanceUrl is null');
    final sfApi =
        SalesforceApi(GetIt.instance<Dio>(), baseUrl: credentials.instanceUrl!);

    final response = await sfApi.contentVersion(contentVersionId);

    // TODO: move this into a ConversationEntryModel method when its moved into the main code body (from live agent kit)
    ConversationEntryAttachment attachment = ConversationEntryAttachment(
      name: response.pathOnClient ?? response.title,
      id: response.id,
      mimeType: mimeTypeFromExtension(response.fileType) ?? response.fileType,
      url: response.versionDataUrl,
      versionId: response.id,
      contentId: response.contentDocumentId,
    );

    return attachment;
  }

  // Future<ContentDocument> getContentDocument(String contentDocumentId) async {
  //   Credentials credentials = await _localStorageRepository.getCredentials();
  //
  //   if (credentials.instanceUrl == null) throw Exception('getContentDocument failure: instanceUrl is null');
  //   final sfApi =
  //       SalesforceApi(GetIt.instance<Dio>(), baseUrl: credentials.instanceUrl!);
  //
  //   final response = await sfApi.contentVersionDocument(contentDocumentId);
  //
  //   return ContentDocument.fromContentDocumentResponse(response);
  // }
}
