import 'package:flutter/foundation.dart';
import 'package:x1440/utils/performance_monitor.dart';

/// A utility class to analyze performance metrics collected by PerformanceMonitor
/// and provide insights into performance bottlenecks
class PerformanceAnalyzer {
  static final PerformanceAnalyzer _instance = PerformanceAnalyzer._internal();
  factory PerformanceAnalyzer() => _instance;
  PerformanceAnalyzer._internal();

  static PerformanceAnalyzer get instance => _instance;
  
  /// Threshold in milliseconds for identifying slow operations (16ms = 60fps frame budget)
  final int slowOperationThreshold = 16;
  
  /// Threshold in milliseconds for identifying very slow operations (100ms = noticeable delay)
  final int verySlowOperationThreshold = 100;
  
  /// Threshold in milliseconds for identifying potential ANR triggers (500ms)
  final int anrRiskThreshold = 500;

  /// Analyze all collected performance metrics and return insights
  Map<String, dynamic> analyzeAllMetrics() {
    final allStats = PerformanceMonitor.instance.getAllStats();
    final slowOperations = <String, dynamic>{};
    final verySlowOperations = <String, dynamic>{};
    final anrRiskOperations = <String, dynamic>{};
    
    for (final entry in allStats.entries) {
      final name = entry.key;
      final stats = entry.value as Map<String, dynamic>;
      
      if (stats['count'] == 0) continue;
      
      // Check if this is a slow operation (avg > slowOperationThreshold)
      if ((stats['avg'] as double) > slowOperationThreshold) {
        slowOperations[name] = stats;
      }
      
      // Check if this is a very slow operation (avg > verySlowOperationThreshold)
      if ((stats['avg'] as double) > verySlowOperationThreshold) {
        verySlowOperations[name] = stats;
      }
      
      // Check if this operation might cause ANRs (max > anrRiskThreshold)
      if ((stats['max'] as int) > anrRiskThreshold) {
        anrRiskOperations[name] = stats;
      }
    }
    
    return {
      'totalMetricsCount': allStats.length,
      'slowOperationsCount': slowOperations.length,
      'verySlowOperationsCount': verySlowOperations.length,
      'anrRiskOperationsCount': anrRiskOperations.length,
      'slowOperations': slowOperations,
      'verySlowOperations': verySlowOperations,
      'anrRiskOperations': anrRiskOperations,
    };
  }
  
  /// Generate a human-readable performance report
  String generateReport({bool logToConsole = true}) {
    final analysis = analyzeAllMetrics();
    final buffer = StringBuffer();
    
    buffer.writeln('===== PERFORMANCE ANALYSIS REPORT =====');
    buffer.writeln('Total metrics collected: ${analysis['totalMetricsCount']}');
    buffer.writeln('Slow operations (>16ms): ${analysis['slowOperationsCount']}');
    buffer.writeln('Very slow operations (>100ms): ${analysis['verySlowOperationsCount']}');
    buffer.writeln('Potential ANR triggers (>500ms): ${analysis['anrRiskOperationsCount']}');
    
    if (analysis['anrRiskOperationsCount'] > 0) {
      buffer.writeln('\n🚨 POTENTIAL ANR TRIGGERS:');
      _writeOperationStats(buffer, analysis['anrRiskOperations'] as Map<String, dynamic>);
    }
    
    if (analysis['verySlowOperationsCount'] > 0) {
      buffer.writeln('\n⚠️ VERY SLOW OPERATIONS:');
      _writeOperationStats(buffer, analysis['verySlowOperations'] as Map<String, dynamic>);
    }
    
    buffer.writeln('\n===== END OF REPORT =====');
    
    final report = buffer.toString();
    if (logToConsole && kDebugMode) {
      print(report);
    }
    
    return report;
  }
  
  /// Find the slowest operations in a specific category
  List<MapEntry<String, dynamic>> findSlowestOperations(String categoryPrefix, {int limit = 5}) {
    final allStats = PerformanceMonitor.instance.getAllStats();
    final filteredStats = allStats.entries
        .where((entry) => entry.key.startsWith(categoryPrefix))
        .toList();
    
    // Sort by average duration (descending)
    filteredStats.sort((a, b) {
      final aStats = a.value as Map<String, dynamic>;
      final bStats = b.value as Map<String, dynamic>;
      return (bStats['avg'] as double).compareTo(aStats['avg'] as double);
    });
    
    return filteredStats.take(limit).toList();
  }
  
  /// Analyze a specific part of the app startup process
  Map<String, dynamic> analyzeStartupPhase(String phasePrefix) {
    final phaseOperations = findSlowestOperations(phasePrefix);
    final totalDuration = phaseOperations.fold<double>(
      0, 
      (sum, entry) {
        final stats = entry.value as Map<String, dynamic>;
        return sum + (stats['avg'] as double);
      }
    );
    
    return {
      'operations': phaseOperations,
      'totalDuration': totalDuration,
    };
  }
  
  /// Generate a report focused on app startup performance
  String generateStartupReport({bool logToConsole = true}) {
    final buffer = StringBuffer();
    buffer.writeln('===== APP STARTUP PERFORMANCE REPORT =====');
    
    // Analyze different startup phases
    final appInit = analyzeStartupPhase('app_init');
    final authPhase = analyzeStartupPhase('auth_');
    final providersPhase = analyzeStartupPhase('providers_');
    final uiBuildPhase = analyzeStartupPhase('build_');
    
    buffer.writeln('App Initialization: ~${appInit['totalDuration'].toStringAsFixed(1)}ms');
    buffer.writeln('Auth Operations: ~${authPhase['totalDuration'].toStringAsFixed(1)}ms');
    buffer.writeln('Providers Setup: ~${providersPhase['totalDuration'].toStringAsFixed(1)}ms');
    buffer.writeln('UI Building: ~${uiBuildPhase['totalDuration'].toStringAsFixed(1)}ms');
    
    buffer.writeln('\nSlowest Auth Operations:');
    _writeOperationStats(buffer, Map.fromEntries(authPhase['operations'] as List<MapEntry<String, dynamic>>));
    
    buffer.writeln('\nSlowest Provider Operations:');
    _writeOperationStats(buffer, Map.fromEntries(providersPhase['operations'] as List<MapEntry<String, dynamic>>));
    
    buffer.writeln('\nSlowest UI Build Operations:');
    _writeOperationStats(buffer, Map.fromEntries(uiBuildPhase['operations'] as List<MapEntry<String, dynamic>>));
    
    buffer.writeln('\n===== END OF STARTUP REPORT =====');
    
    final report = buffer.toString();
    if (logToConsole && kDebugMode) {
      print(report);
    }
    
    return report;
  }
  
  /// Write operation statistics in a formatted way
  void _writeOperationStats(StringBuffer buffer, Map<String, dynamic> operations) {
    final sortedEntries = operations.entries.toList()
      ..sort((a, b) {
        final aStats = a.value as Map<String, dynamic>;
        final bStats = b.value as Map<String, dynamic>;
        return (bStats['avg'] as double).compareTo(aStats['avg'] as double);
      });
    
    for (final entry in sortedEntries) {
      final name = entry.key;
      final stats = entry.value as Map<String, dynamic>;
      buffer.writeln(
        '  - $name: avg=${stats['avg'].toStringAsFixed(1)}ms, '
        'med=${stats['median'].toStringAsFixed(1)}ms, '
        'max=${stats['max']}ms (${stats['count']} samples)'
      );
    }
  }
  
  /// Get recommendations for fixing ANR issues based on analysis
  List<String> getAnrRecommendations() {
    final analysis = analyzeAllMetrics();
    final recommendations = <String>[];
    
    if (analysis['anrRiskOperationsCount'] > 0) {
      recommendations.add('Consider moving these operations to a background thread or isolate:');
      final anrOps = analysis['anrRiskOperations'] as Map<String, dynamic>;
      for (final entry in anrOps.entries.take(3)) {
        recommendations.add('  - ${entry.key}');
      }
    }
    
    // Check for auth operations
    final authOps = findSlowestOperations('auth_', limit: 3);
    if (authOps.isNotEmpty && authOps.any((e) => 
        ((e.value as Map<String, dynamic>)['avg'] as double) > verySlowOperationThreshold)) {
      recommendations.add(
        'Auth operations are slow. Consider optimizing authentication flow and ensuring '
        'network calls use the network isolate correctly.'
      );
    }
    
    // Check for provider initialization
    final providerOps = findSlowestOperations('providers_', limit: 3);
    if (providerOps.isNotEmpty && providerOps.any((e) => 
        ((e.value as Map<String, dynamic>)['avg'] as double) > verySlowOperationThreshold)) {
      recommendations.add(
        'Provider initialization is slow. Consider lazy loading providers or '
        'deferring non-critical providers initialization.'
      );
    }
    
    // Check for UI operations
    final uiBuildOps = findSlowestOperations('build_', limit: 3);
    if (uiBuildOps.isNotEmpty && uiBuildOps.any((e) => 
        ((e.value as Map<String, dynamic>)['avg'] as double) > verySlowOperationThreshold)) {
      recommendations.add(
        'UI building operations are slow. Consider optimizing widget trees, '
        'using const widgets where possible, and implementing caching for expensive computations.'
      );
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('No significant performance issues detected.');
    }
    
    return recommendations;
  }
}
