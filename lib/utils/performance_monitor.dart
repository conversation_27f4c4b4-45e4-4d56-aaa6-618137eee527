import 'dart:collection';
import 'package:flutter/foundation.dart';

/// A utility class to monitor performance across the app
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  static PerformanceMonitor get instance => _instance;

  final Map<String, DateTime> _startTimes = {};
  final Map<String, int> _durations = {};
  final Map<String, List<int>> _multiMeasurements = {};
  final Queue<String> _logBuffer = Queue<String>();
  final int _maxBufferSize = 100;
  bool _flushScheduled = false;

  /// Start measuring a performance metric
  void startMeasure(String name) {
    _startTimes[name] = DateTime.now();
  }

  /// End measuring a performance metric and record the duration
  int endMeasure(String name, {bool log = true}) {
    final endTime = DateTime.now();
    final startTime = _startTimes.remove(name);
    
    if (startTime == null) {
      _bufferLog('⚠️ No start time found for performance measure: $name');
      return -1;
    }
    
    final duration = endTime.difference(startTime).inMilliseconds;
    _durations[name] = duration;
    
    if (!_multiMeasurements.containsKey(name)) {
      _multiMeasurements[name] = [];
    }
    _multiMeasurements[name]!.add(duration);
    
    if (log) {
      _bufferLog('⏱️ PERF - $name: ${duration}ms');
    }
    
    return duration;
  }

  /// Get the duration of a performance metric
  int? getDuration(String name) {
    return _durations[name];
  }

  /// Add to multi-measurement series without starting/ending
  void addMeasurement(String name, int durationMs, {bool log = true}) {
    if (!_multiMeasurements.containsKey(name)) {
      _multiMeasurements[name] = [];
    }
    _multiMeasurements[name]!.add(durationMs);
    
    if (log) {
      _bufferLog('⏱️ PERF - $name: ${durationMs}ms');
    }
  }

  /// Get statistics for a performance metric
  Map<String, dynamic> getStats(String name) {
    final measurements = _multiMeasurements[name];
    if (measurements == null || measurements.isEmpty) {
      return {'count': 0};
    }
    
    measurements.sort();
    final count = measurements.length;
    final min = measurements.first;
    final max = measurements.last;
    final sum = measurements.reduce((a, b) => a + b);
    final avg = sum / count;
    
    final median = count % 2 == 0
        ? (measurements[count ~/ 2 - 1] + measurements[count ~/ 2]) / 2
        : measurements[count ~/ 2].toDouble();
    
    return {
      'count': count,
      'min': min,
      'max': max,
      'avg': avg,
      'median': median,
    };
  }

  /// Get all recorded performance metrics
  Map<String, dynamic> getAllStats() {
    final result = <String, dynamic>{};
    for (final name in _multiMeasurements.keys) {
      result[name] = getStats(name);
    }
    return result;
  }

  /// Reset all performance metrics
  void reset() {
    _startTimes.clear();
    _durations.clear();
    _multiMeasurements.clear();
  }
  
  /// Add a log to the buffer
  void _bufferLog(String message) {
    _logBuffer.add(message);
    if (_logBuffer.length > _maxBufferSize) {
      _logBuffer.removeFirst();
    }
    
    if (!_flushScheduled) {
      _flushScheduled = true;
      Future.delayed(const Duration(milliseconds: 500), _flushLogs);
    }
  }
  
  /// Flush the log buffer to the console
  void _flushLogs() {
    _flushScheduled = false;
    if (_logBuffer.isEmpty) return;
    
    final buffer = StringBuffer();
    buffer.writeln('===== PERFORMANCE REPORT =====');
    for (final log in _logBuffer) {
      buffer.writeln(log);
    }
    buffer.writeln('=============================');
    
    if (kDebugMode) {
      print(buffer.toString());
    }
    
    _logBuffer.clear();
  }
  
  /// Directly log a performance message
  void log(String message) {
    _bufferLog('📊 PERF - $message');
  }
  
  /// Convenience method for wrapping a function with performance measurement
  T measure<T>(String name, T Function() function, {bool log = true}) {
    startMeasure(name);
    final result = function();
    endMeasure(name, log: log);
    return result;
  }
  
  /// Convenience method for wrapping an async function with performance measurement
  Future<T> measureAsync<T>(String name, Future<T> Function() function, {bool log = true}) async {
    startMeasure(name);
    try {
      final result = await function();
      endMeasure(name, log: log);
      return result;
    } catch (e) {
      endMeasure('${name}_error', log: log);
      rethrow;
    }
  }
}
