import 'package:flutter/material.dart';
import 'package:x1440/utils/performance_monitor.dart';

/// A widget that wraps a child widget and measures its build time
class PerformanceWrapper extends StatefulWidget {
  final Widget child;
  final String tag;
  final bool logToConsole;
  
  /// Creates a widget that measures the build time of its child
  ///
  /// [tag] is used to identify this measurement in logs
  /// [child] is the widget to be measured
  /// [logToConsole] whether to print the measurement to the console
  const PerformanceWrapper({
    Key? key,
    required this.child,
    required this.tag,
    this.logToConsole = true,
  }) : super(key: key);
  
  @override
  State<PerformanceWrapper> createState() => _PerformanceWrapperState();
}

class _PerformanceWrapperState extends State<PerformanceWrapper> {
  late final String _buildTag;
  late final String _initTag;
  
  @override
  void initState() {
    super.initState();
    _initTag = "${widget.tag}_init";
    _buildTag = "${widget.tag}_build";
    
    // Measure initialization time
    PerformanceMonitor.instance.log("Initializing ${widget.tag}");
    PerformanceMonitor.instance.startMeasure(_initTag);
  }

  @override
  void dispose() {
    // If we're still measuring initialization when disposed, end it
    if (PerformanceMonitor.instance.getDuration(_initTag) == null) {
      PerformanceMonitor.instance.endMeasure(_initTag, log: widget.logToConsole);
    }
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // End initialization timing on first dependencies change
    if (PerformanceMonitor.instance.getDuration(_initTag) == null) {
      PerformanceMonitor.instance.endMeasure(_initTag, log: widget.logToConsole);
    }
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    // Start timing the build
    PerformanceMonitor.instance.startMeasure(_buildTag);
    
    // Use a post-frame callback to end the timing after the frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PerformanceMonitor.instance.endMeasure(_buildTag, log: widget.logToConsole);
    });
    
    return widget.child;
  }
}

/// A stateless widget that wraps a builder function and measures its build time
class PerformanceBuilder extends StatelessWidget {
  final WidgetBuilder builder;
  final String tag;
  final bool logToConsole;
  
  /// Creates a widget that measures the build time of the widget created by [builder]
  ///
  /// [tag] is used to identify this measurement in logs
  /// [builder] is the function that creates the widget to be measured
  /// [logToConsole] whether to print the measurement to the console
  const PerformanceBuilder({
    Key? key,
    required this.builder,
    required this.tag,
    this.logToConsole = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final startTime = DateTime.now();
    final result = builder(context);
    
    // Don't block the build method with performance logging
    Future.microtask(() {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      PerformanceMonitor.instance.addMeasurement(
        tag, 
        duration, 
        log: logToConsole
      );
    });
    
    return result;
  }
}
