import 'package:flutter/services.dart';

/// Utility class for platform-specific operations
class PlatformUtils {
  /// Method channel for app launch checks
  static const platform = MethodChannel('io.x1440/app_launch');
  
  /// Checks if the app was launched from a notification
  static Future<bool> wasLaunchedFromNotification() async {
    try {
      final result = await platform.invokeMethod<bool>('wasLaunchedFromNotification');
      return result ?? false;
    } on PlatformException catch (e) {
      print('Error checking launch type: ${e.message}');
      // Default to false if we can't determine the launch source
      return false;
    }
  }
}
