import 'dart:ui';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/utils/converters/color_converter.dart';

part 'toast_options.freezed.dart';
part 'toast_options.g.dart';

@freezed
abstract class ToastOptions with _$ToastOptions {
  const factory ToastOptions({
    /// Android Only
    Toast? toastLength,
    /// position
    ToastGravity? gravity,
    /// iOS & Web Only
    int? timeInSecForIosWeb,
    @ColorConverter() Color? backgroundColor,
    @ColorConverter() Color? textColor,
    double? fontSize,
  }) = _ToastOptions;
  
  factory ToastOptions.fromJson(Map<String, dynamic> json) => _$ToastOptionsFromJson(json);
}