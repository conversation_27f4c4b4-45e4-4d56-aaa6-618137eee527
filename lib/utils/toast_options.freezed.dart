// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'toast_options.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ToastOptions {
  /// Android Only
  Toast? get toastLength;

  /// position
  ToastGravity? get gravity;

  /// iOS & Web Only
  int? get timeInSecForIosWeb;
  @ColorConverter()
  Color? get backgroundColor;
  @ColorConverter()
  Color? get textColor;
  double? get fontSize;

  /// Create a copy of ToastOptions
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ToastOptionsCopyWith<ToastOptions> get copyWith =>
      _$ToastOptionsCopyWithImpl<ToastOptions>(
          this as ToastOptions, _$identity);

  /// Serializes this ToastOptions to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ToastOptions &&
            (identical(other.toastLength, toastLength) ||
                other.toastLength == toastLength) &&
            (identical(other.gravity, gravity) || other.gravity == gravity) &&
            (identical(other.timeInSecForIosWeb, timeInSecForIosWeb) ||
                other.timeInSecForIosWeb == timeInSecForIosWeb) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.textColor, textColor) ||
                other.textColor == textColor) &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, toastLength, gravity,
      timeInSecForIosWeb, backgroundColor, textColor, fontSize);

  @override
  String toString() {
    return 'ToastOptions(toastLength: $toastLength, gravity: $gravity, timeInSecForIosWeb: $timeInSecForIosWeb, backgroundColor: $backgroundColor, textColor: $textColor, fontSize: $fontSize)';
  }
}

/// @nodoc
abstract mixin class $ToastOptionsCopyWith<$Res> {
  factory $ToastOptionsCopyWith(
          ToastOptions value, $Res Function(ToastOptions) _then) =
      _$ToastOptionsCopyWithImpl;
  @useResult
  $Res call(
      {Toast? toastLength,
      ToastGravity? gravity,
      int? timeInSecForIosWeb,
      @ColorConverter() Color? backgroundColor,
      @ColorConverter() Color? textColor,
      double? fontSize});
}

/// @nodoc
class _$ToastOptionsCopyWithImpl<$Res> implements $ToastOptionsCopyWith<$Res> {
  _$ToastOptionsCopyWithImpl(this._self, this._then);

  final ToastOptions _self;
  final $Res Function(ToastOptions) _then;

  /// Create a copy of ToastOptions
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? toastLength = freezed,
    Object? gravity = freezed,
    Object? timeInSecForIosWeb = freezed,
    Object? backgroundColor = freezed,
    Object? textColor = freezed,
    Object? fontSize = freezed,
  }) {
    return _then(_self.copyWith(
      toastLength: freezed == toastLength
          ? _self.toastLength
          : toastLength // ignore: cast_nullable_to_non_nullable
              as Toast?,
      gravity: freezed == gravity
          ? _self.gravity
          : gravity // ignore: cast_nullable_to_non_nullable
              as ToastGravity?,
      timeInSecForIosWeb: freezed == timeInSecForIosWeb
          ? _self.timeInSecForIosWeb
          : timeInSecForIosWeb // ignore: cast_nullable_to_non_nullable
              as int?,
      backgroundColor: freezed == backgroundColor
          ? _self.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      textColor: freezed == textColor
          ? _self.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      fontSize: freezed == fontSize
          ? _self.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ToastOptions].
extension ToastOptionsPatterns on ToastOptions {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ToastOptions value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ToastOptions() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ToastOptions value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ToastOptions():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ToastOptions value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ToastOptions() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Toast? toastLength,
            ToastGravity? gravity,
            int? timeInSecForIosWeb,
            @ColorConverter() Color? backgroundColor,
            @ColorConverter() Color? textColor,
            double? fontSize)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ToastOptions() when $default != null:
        return $default(
            _that.toastLength,
            _that.gravity,
            _that.timeInSecForIosWeb,
            _that.backgroundColor,
            _that.textColor,
            _that.fontSize);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Toast? toastLength,
            ToastGravity? gravity,
            int? timeInSecForIosWeb,
            @ColorConverter() Color? backgroundColor,
            @ColorConverter() Color? textColor,
            double? fontSize)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ToastOptions():
        return $default(
            _that.toastLength,
            _that.gravity,
            _that.timeInSecForIosWeb,
            _that.backgroundColor,
            _that.textColor,
            _that.fontSize);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Toast? toastLength,
            ToastGravity? gravity,
            int? timeInSecForIosWeb,
            @ColorConverter() Color? backgroundColor,
            @ColorConverter() Color? textColor,
            double? fontSize)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ToastOptions() when $default != null:
        return $default(
            _that.toastLength,
            _that.gravity,
            _that.timeInSecForIosWeb,
            _that.backgroundColor,
            _that.textColor,
            _that.fontSize);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ToastOptions implements ToastOptions {
  const _ToastOptions(
      {this.toastLength,
      this.gravity,
      this.timeInSecForIosWeb,
      @ColorConverter() this.backgroundColor,
      @ColorConverter() this.textColor,
      this.fontSize});
  factory _ToastOptions.fromJson(Map<String, dynamic> json) =>
      _$ToastOptionsFromJson(json);

  /// Android Only
  @override
  final Toast? toastLength;

  /// position
  @override
  final ToastGravity? gravity;

  /// iOS & Web Only
  @override
  final int? timeInSecForIosWeb;
  @override
  @ColorConverter()
  final Color? backgroundColor;
  @override
  @ColorConverter()
  final Color? textColor;
  @override
  final double? fontSize;

  /// Create a copy of ToastOptions
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ToastOptionsCopyWith<_ToastOptions> get copyWith =>
      __$ToastOptionsCopyWithImpl<_ToastOptions>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ToastOptionsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ToastOptions &&
            (identical(other.toastLength, toastLength) ||
                other.toastLength == toastLength) &&
            (identical(other.gravity, gravity) || other.gravity == gravity) &&
            (identical(other.timeInSecForIosWeb, timeInSecForIosWeb) ||
                other.timeInSecForIosWeb == timeInSecForIosWeb) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.textColor, textColor) ||
                other.textColor == textColor) &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, toastLength, gravity,
      timeInSecForIosWeb, backgroundColor, textColor, fontSize);

  @override
  String toString() {
    return 'ToastOptions(toastLength: $toastLength, gravity: $gravity, timeInSecForIosWeb: $timeInSecForIosWeb, backgroundColor: $backgroundColor, textColor: $textColor, fontSize: $fontSize)';
  }
}

/// @nodoc
abstract mixin class _$ToastOptionsCopyWith<$Res>
    implements $ToastOptionsCopyWith<$Res> {
  factory _$ToastOptionsCopyWith(
          _ToastOptions value, $Res Function(_ToastOptions) _then) =
      __$ToastOptionsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Toast? toastLength,
      ToastGravity? gravity,
      int? timeInSecForIosWeb,
      @ColorConverter() Color? backgroundColor,
      @ColorConverter() Color? textColor,
      double? fontSize});
}

/// @nodoc
class __$ToastOptionsCopyWithImpl<$Res>
    implements _$ToastOptionsCopyWith<$Res> {
  __$ToastOptionsCopyWithImpl(this._self, this._then);

  final _ToastOptions _self;
  final $Res Function(_ToastOptions) _then;

  /// Create a copy of ToastOptions
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? toastLength = freezed,
    Object? gravity = freezed,
    Object? timeInSecForIosWeb = freezed,
    Object? backgroundColor = freezed,
    Object? textColor = freezed,
    Object? fontSize = freezed,
  }) {
    return _then(_ToastOptions(
      toastLength: freezed == toastLength
          ? _self.toastLength
          : toastLength // ignore: cast_nullable_to_non_nullable
              as Toast?,
      gravity: freezed == gravity
          ? _self.gravity
          : gravity // ignore: cast_nullable_to_non_nullable
              as ToastGravity?,
      timeInSecForIosWeb: freezed == timeInSecForIosWeb
          ? _self.timeInSecForIosWeb
          : timeInSecForIosWeb // ignore: cast_nullable_to_non_nullable
              as int?,
      backgroundColor: freezed == backgroundColor
          ? _self.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      textColor: freezed == textColor
          ? _self.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      fontSize: freezed == fontSize
          ? _self.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

// dart format on
