// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'toast_options.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ToastOptions _$ToastOptionsFromJson(Map json) => $checkedCreate(
      '_ToastOptions',
      json,
      ($checkedConvert) {
        final val = _ToastOptions(
          toastLength: $checkedConvert(
              'toastLength', (v) => $enumDecodeNullable(_$ToastEnumMap, v)),
          gravity: $checkedConvert(
              'gravity', (v) => $enumDecodeNullable(_$ToastGravityEnumMap, v)),
          timeInSecForIosWeb: $checkedConvert(
              'timeInSecForIosWeb', (v) => (v as num?)?.toInt()),
          backgroundColor: $checkedConvert('backgroundColor',
              (v) => const ColorConverter().fromJson((v as num?)?.toInt())),
          textColor: $checkedConvert('textColor',
              (v) => const ColorConverter().fromJson((v as num?)?.toInt())),
          fontSize: $checkedConvert('fontSize', (v) => (v as num?)?.toDouble()),
        );
        return val;
      },
    );

Map<String, dynamic> _$ToastOptionsToJson(_ToastOptions instance) =>
    <String, dynamic>{
      if (_$ToastEnumMap[instance.toastLength] case final value?)
        'toastLength': value,
      if (_$ToastGravityEnumMap[instance.gravity] case final value?)
        'gravity': value,
      if (instance.timeInSecForIosWeb case final value?)
        'timeInSecForIosWeb': value,
      if (const ColorConverter().toJson(instance.backgroundColor)
          case final value?)
        'backgroundColor': value,
      if (const ColorConverter().toJson(instance.textColor) case final value?)
        'textColor': value,
      if (instance.fontSize case final value?) 'fontSize': value,
    };

const _$ToastEnumMap = {
  Toast.LENGTH_SHORT: 'LENGTH_SHORT',
  Toast.LENGTH_LONG: 'LENGTH_LONG',
};

const _$ToastGravityEnumMap = {
  ToastGravity.TOP: 'TOP',
  ToastGravity.BOTTOM: 'BOTTOM',
  ToastGravity.CENTER: 'CENTER',
  ToastGravity.TOP_LEFT: 'TOP_LEFT',
  ToastGravity.TOP_RIGHT: 'TOP_RIGHT',
  ToastGravity.BOTTOM_LEFT: 'BOTTOM_LEFT',
  ToastGravity.BOTTOM_RIGHT: 'BOTTOM_RIGHT',
  ToastGravity.CENTER_LEFT: 'CENTER_LEFT',
  ToastGravity.CENTER_RIGHT: 'CENTER_RIGHT',
  ToastGravity.SNACKBAR: 'SNACKBAR',
  ToastGravity.NONE: 'NONE',
};
