import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'package:x1440/debug/debug_viewmodel.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart';
import 'package:x1440/services/interfaces/productivity_tools_viewmodel_interface.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_event.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/auth/auth_state.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_manager.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_state.dart';
import 'package:x1440/ui/error/app_blocking_errors_view.dart';
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/ui/themes/themes.dart';
import 'package:x1440/use_cases/models/credentials.dart';

import 'conversations_viewmodel.dart';
import 'deeplink_viewmodel.dart';

/// Provides the app-wide ViewModel structure, essentially building the main application widget tree
/// with all required providers, blocs, and state management
Widget getAppWideViewModels({bool liveActivitiesAreEnabled = false}) {
  print('⏱️ UI PERF - getAppWideViewModels START ${DateTime.now().toString()}');
  
  return BlocBuilder<DemoModeManager, DemoModeState>(
    bloc: GetIt.I<DemoModeManager>(),
    buildWhen: (previous, current) => previous.toggle != current.toggle,
    builder: (context, demoModeState) {
      // Get router
      final routerStartTime = DateTime.now();
      final router = GetIt.I<RoutingManager>().router;
      print('⏱️ UI PERF - Get router took ${DateTime.now().difference(routerStartTime).inMilliseconds}ms');
      
      // Error reporting helper function
      void reportError(AppError error) {
        try {
          if (GetIt.I.isRegistered<AppErrorBloc>()) {
            GetIt.I<AppErrorBloc>().add(ReportAppErrorEvent(error));
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error reporting error: $e');
          }
        }
      }
      
      final providersStartTime = DateTime.now();
      print('⏱️ UI PERF - Starting MultiProvider setup');
      return MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (context) => DebugViewModel(reportError: reportError)
          ),
          ChangeNotifierProvider(
            create: (context) => DeepLinkViewModel(router),
            lazy: false,
          ),
          BlocProvider.value(value: GetIt.I<NotificationManager>()),
          BlocProvider.value(value: GetIt.I<PresenceManager>()),
          ChangeNotifierProvider<ProductivityToolsViewmodelInterface>.value(
            value: GetIt.I<ProductivityToolsViewmodelInterface>(),
          ),
          ChangeNotifierProvider<ContactsViewmodelInterface>.value(
            value: GetIt.I<ContactsViewmodelInterface>(),
          ),
        ],
        builder: (context, _) {
          print(' UI PERF - MultiProvider setup took ${DateTime.now().difference(providersStartTime).inMilliseconds}ms');
          final childBuildStartTime = DateTime.now();
          print(' UI PERF - Building core app widget tree');
          Widget child = Directionality(
            textDirection: TextDirection.ltr,
            child: Stack(
              children: [
                MaterialApp.router(
                  localizationsDelegates: const [
                    S.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  supportedLocales: S.delegate.supportedLocales,
                  debugShowCheckedModeBanner: false,
                  routerDelegate: router.routerDelegate,
                  routeInformationParser: router.routeInformationParser,
                  routeInformationProvider: router.routeInformationProvider,
                  themeMode: ThemeMode.system,
                  theme: lightThemeData(context),
                  darkTheme: darkThemeData(context),
                  title: "X1440",
                ),
                const Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: AppOverlaysLayer())
              ],
            ),
          );

          print(' UI PERF - Child widget tree took ${DateTime.now().difference(childBuildStartTime).inMilliseconds}ms');
          
          print(' UI PERF - Setting up AuthBloc provider');
          return BlocProvider<AuthBloc>.value(
            value: () {
              /// Adding short delay to avoid Android ANRs
              final getAuthBlocStartTime = DateTime.now();
              final authBloc = GetIt.I<AuthBloc>();
              print(' UI PERF - GetIt.I<AuthBloc>() took ${DateTime.now().difference(getAuthBlocStartTime).inMilliseconds}ms');

              WidgetsBinding.instance.addPostFrameCallback((_) {
                print(' UI PERF - PostFrameCallback executed');
                // Significantly delay auto-login to prevent blocking app startup
                // The expensive token encryption should not block the initial UI render
                Future.delayed(const Duration(seconds: 3), () {
                  // handleAppOpen(_appLifeCycleRepository.appLifecycleState);
                  scheduleMicrotask(() {
                    authBloc.add(AttemptAutoLoginEvent());
                  });
                });
              });
              return authBloc;
            }(),
            child: BlocConsumer<AuthBloc, AuthState>(
              listenWhen: (previous, current) {
                print(' UI PERF - AuthBloc state changed: ${previous.runtimeType} -> ${current.runtimeType}');
                return true;
              },
              listener: (context, authState) {
                if (authState.logIn?.consume() != null) {
                  // TODO: fix this -- doing this here caused a race condition with the future builder below ... the /conversations screen needs the providers that are created after login (and need the credentials); For now this post-login routing is handled in the Router redirect
                  // GetIt.I<RoutingManager>().router.go('/conversations');
                } else if (authState.logOut?.consume() != null) {
                  GetIt.I<RoutingManager>()
                      .router
                      .go('/', extra: authState.appError);
                }
              },
              builder: (context, authState) {
                print(' UI PERF - AuthBloc builder called with state: ${authState.runtimeType}');
                final authWidgetBuildStartTime = DateTime.now();

                if (authState.isLoggingIn || authState.isLoggingOut) {
                  return Directionality(
                    textDirection: TextDirection.ltr,
                    child: Scaffold(
                      backgroundColor: ThemeConstants.colors.appBlack,
                      body: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  );
                }

                print(' UI PERF - Auth widget build took ${DateTime.now().difference(authWidgetBuildStartTime).inMilliseconds}ms');
                if (authState.isLoggedIn != true) {
                  return child; // If we're not logged in, we have everything we need
                } else {
                  print(' UI PERF - Starting FutureBuilder for credentials');
                  final futureBuilderStartTime = DateTime.now();
                  return FutureBuilder<Credentials?>(
                    future: GetIt.I<LocalStorageRepository>().getCredentials(),
                    builder: (context, creds) {
                      print(' UI PERF - FutureBuilder completed in ${DateTime.now().difference(futureBuilderStartTime).inMilliseconds}ms');
                      Credentials? credentials = creds.data;
                      if (credentials == null || credentials.sfLoggedIn == false) {
                        return child;
                      }

                      final loggedInProvidersStartTime = DateTime.now();
                      print(' UI PERF - Setting up logged-in MultiProvider');
                      return MultiProvider(
                        providers: [
                          ChangeNotifierProvider<ConversationsViewmodel>.value(
                            value: GetIt.I<ConversationsViewmodel>(),
                          ),
                        ],
                        child: child,
                        builder: (context, widget) {
                          print(' UI PERF - Logged-in MultiProvider setup took ${DateTime.now().difference(loggedInProvidersStartTime).inMilliseconds}ms');
                          return widget ?? const SizedBox();
                        },
                      );
                    },
                  );
                }
              },
            ),
          );
        },
      );
    },
  );
}
