import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
// Router is accessed through RoutingManager
import 'package:provider/provider.dart';
import 'package:x1440/debug/debug_viewmodel.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart';
import 'package:x1440/services/interfaces/productivity_tools_viewmodel_interface.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_event.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/auth/auth_state.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_manager.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_state.dart';
// AppBlockingErrorsView is not directly used
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/ui/themes/themes.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/utils/performance_monitor.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'conversations_viewmodel.dart';
import 'deeplink_viewmodel.dart';

/// Provides the app-wide ViewModel structure, essentially building the main application widget tree
/// with all required providers, blocs, and state management
Widget getAppWideViewModels({bool liveActivitiesAreEnabled = false}) {
  // Start overall performance measurement
  final perfMon = PerformanceMonitor.instance;
  perfMon.startMeasure('getAppWideViewModels_total');
  
  return BlocBuilder<DemoModeManager, DemoModeState>(
    bloc: GetIt.I<DemoModeManager>(),
    buildWhen: (previous, current) => previous.toggle != current.toggle,
    builder: (context, demoModeState) {
      // Get router
      perfMon.startMeasure('get_router');
      // Use router in the builder
      final goRouter = GetIt.I<RoutingManager>().router;
      perfMon.endMeasure('get_router');

      // Error reporting helper function
      void reportError(AppError error) {
        try {
          if (GetIt.I.isRegistered<AppErrorBloc>()) {
            GetIt.I<AppErrorBloc>().add(ReportAppErrorEvent(error));
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error reporting error: $e');
          }
        }
      }
      
      // Set up base MultiProvider
      perfMon.startMeasure('providers_setup');
      return MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (context) => DebugViewModel(reportError: reportError),
          ),
          ChangeNotifierProvider(
            create: (context) => DeepLinkViewModel(goRouter),
            lazy: false,
          ),
          BlocProvider.value(value: GetIt.I<NotificationManager>()),
          BlocProvider.value(value: GetIt.I<PresenceManager>()),
          ChangeNotifierProvider<ProductivityToolsViewmodelInterface>.value(
            value: GetIt.I<ProductivityToolsViewmodelInterface>(),
          ),
          ChangeNotifierProvider<ContactsViewmodelInterface>.value(
            value: GetIt.I<ContactsViewmodelInterface>(),
          ),
        ],
        builder: (context, child) {
          perfMon.endMeasure('providers_setup');
          
          // Build core app widget
          perfMon.startMeasure('core_app_build');
          final coreWidget = Directionality(
            textDirection: TextDirection.ltr,
            child: Stack(
              children: [
                // Wrap MaterialApp in a function to avoid Flutter trying to build it during layout
                Builder(builder: (context) {
                  perfMon.startMeasure('material_app_build');
                  final app = MaterialApp.router(
                    localizationsDelegates: const [
                      S.delegate,
                      GlobalMaterialLocalizations.delegate,
                      GlobalWidgetsLocalizations.delegate,
                      GlobalCupertinoLocalizations.delegate,
                    ],
                    supportedLocales: S.delegate.supportedLocales,
                    debugShowCheckedModeBanner: false,
                    routerDelegate: goRouter.routerDelegate,
                    routeInformationParser: goRouter.routeInformationParser,
                    routeInformationProvider: goRouter.routeInformationProvider,
                    themeMode: ThemeMode.light,
                    theme: lightThemeData(context),
                    darkTheme: darkThemeData(context),
                    title: "X1440",
                  );
                  perfMon.endMeasure('material_app_build');
                  return app;
                }),
              ],
            ),
          );
          perfMon.endMeasure('core_app_build');
          
          // Set up AuthBloc
          perfMon.startMeasure('auth_bloc_setup');
          return BlocProvider<AuthBloc>.value(
            value: () {
              // Add a short delay to ensure proper initialization to avoid ANRs
              perfMon.startMeasure('get_auth_bloc');
              final authBloc = GetIt.I<AuthBloc>();
              perfMon.endMeasure('get_auth_bloc');
              
              // Defer auto-login attempt until after UI rendering to avoid ANR
              perfMon.startMeasure('check_auth_state');
              if (!authBloc.state.isLoggingIn) {
                // Use a longer delay to ensure UI is fully rendered before triggering auth operations
                Future.delayed(const Duration(milliseconds: 500), () {
                  perfMon.startMeasure('dispatch_auto_login');
                  perfMon.log('🔒 DEFERRED AUTH - Triggering auto-login after UI render');
                  authBloc.add(AttemptAutoLoginEvent());
                  perfMon.endMeasure('dispatch_auto_login');
                });
              }
              perfMon.endMeasure('check_auth_state');
              
              return authBloc;
            }(),
            child: BlocConsumer<AuthBloc, AuthState>(
              listenWhen: (previous, current) {
                perfMon.log("AuthBloc state changed: ${previous.runtimeType} -> ${current.runtimeType}");
                return true;
              },
              listener: (context, authState) {
                perfMon.startMeasure('auth_bloc_listener');
                
                // Handle login and logout events
                if (authState.logIn?.consume() != null) {
                  // Post-login routing is handled by the Router redirect
                } else if (authState.logOut?.consume() != null) {
                  GetIt.I<RoutingManager>().router.go('/', extra: authState.appError);
                }
                
                perfMon.endMeasure('auth_bloc_listener');
              },
              builder: (context, authState) {
                perfMon.startMeasure('auth_bloc_builder_${authState.runtimeType.toString()}');
                
                // Show loading state
                if (authState.isLoggingIn || authState.isLoggingOut) {
                  final loadingWidget = Directionality(
                    textDirection: TextDirection.ltr,
                    child: Scaffold(
                      backgroundColor: ThemeConstants.colors.appBlack,
                      body: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  );
                  perfMon.endMeasure('auth_bloc_builder_${authState.runtimeType.toString()}');
                  return loadingWidget;
                }
                
                // If not logged in, return the core widget
                if (authState.isLoggedIn != true) {
                  perfMon.endMeasure('auth_bloc_builder_${authState.runtimeType.toString()}');
                  return coreWidget;
                } 
                
                // If logged in, set up credentials and additional providers
                perfMon.startMeasure('credentials_future_builder');
                return FutureBuilder(
                  future: GetIt.I<LocalStorageRepository>().getCredentials(),
                  builder: (context, creds) {
                    perfMon.endMeasure('credentials_future_builder');
                    
                    // Extract credentials
                    Credentials? credentials = creds.data;
                    if (credentials == null || credentials.sfLoggedIn == false) {
                      return coreWidget;
                    }
                    
                    // Set up additional providers needed for logged-in state
                    perfMon.startMeasure('logged_in_providers_setup');
                    final loggedInWidget = MultiProvider(
                      providers: [
                        ChangeNotifierProvider<ConversationsViewmodel>.value(
                          value: GetIt.I<ConversationsViewmodel>()),
                      ],
                      child: coreWidget,
                      builder: (context, widget) {
                        perfMon.endMeasure('logged_in_providers_setup');
                        return widget ?? const SizedBox();
                      },
                    );
                    
                    return loggedInWidget;
                  },
                );
              },
            ),
          );
        },
      );
    },
  );
}
