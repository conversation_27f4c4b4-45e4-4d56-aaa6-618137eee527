import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AuthInterceptor KeepAlive RegExp Tests', () {
    test('keepAliveRegExp should match keep-alive endpoint correctly', () {
      // Define the regex pattern (same as in auth_interceptor.dart)
      final keepAliveRegExp = RegExp(
          r'^/shim-service/organizations/([^/]+)/sessions/([^/]+)/actions/keep-alive$');

      // Test cases that should match
      const validKeepAlivePaths = [
        '/shim-service/organizations/00DHn0000019Y8b/sessions/tt.Z0pMaVFLemR2TFpuQ2lDX3BuMDRiak5udHJ6ZHJFRGlrbEtaSUxmdzBiY2c1OXFkcnljVVh0N2h6UWtBM2VIS2JpR0NKeVViTlVhUllYMldMUTFzU0MzdXFCMWQ4MlY4TXpuZG1WNkhkRHhiZDZtQ3VqSUplaUpIajQ1T0F4QzdDTDh1aTc1XzlDQUxKWWZNSHVoZ2M0bG9LZGxSSmRSTG1uTzdfTmwyQ01CNUlNY0ktQW5NYmU0PQ/actions/keep-alive',
        '/shim-service/organizations/testOrg123/sessions/shortToken/actions/keep-alive',
        '/shim-service/organizations/ABC123/sessions/xyz789/actions/keep-alive',
      ];

      // Test cases that should NOT match
      const invalidPaths = [
        '/shim-service/organizations/00DHn0000019Y8b/sessions/token123',
        '/shim-service/organizations/00DHn0000019Y8b/sessions/token123/actions/other',
        '/shim-service/organizations/00DHn0000019Y8b/sessions',
        '/shim-service/organizations/00DHn0000019Y8b/sessions/token123/actions/keep-alive/extra',
        '/other-service/organizations/00DHn0000019Y8b/sessions/token123/actions/keep-alive',
        'shim-service/organizations/00DHn0000019Y8b/sessions/token123/actions/keep-alive', // missing leading slash
      ];

      // Test valid paths
      for (final path in validKeepAlivePaths) {
        expect(keepAliveRegExp.hasMatch(path), isTrue,
            reason: 'Should match keep-alive path: $path');
      }

      // Test invalid paths
      for (final path in invalidPaths) {
        expect(keepAliveRegExp.hasMatch(path), isFalse,
            reason: 'Should NOT match path: $path');
      }
    });

    test('keepAliveRegExp should extract orgId and sessionToken correctly', () {
      final keepAliveRegExp = RegExp(
          r'^/shim-service/organizations/([^/]+)/sessions/([^/]+)/actions/keep-alive$');

      const testPath = '/shim-service/organizations/00DHn0000019Y8b/sessions/tt.testSessionToken123/actions/keep-alive';

      final match = keepAliveRegExp.firstMatch(testPath);
      expect(match, isNotNull);
      expect(match!.groupCount, equals(2));
      expect(match.group(1), equals('00DHn0000019Y8b')); // orgId
      expect(match.group(2), equals('tt.testSessionToken123')); // sessionToken
    });

    test('swapSessionTokenIfNeeded should work with keep-alive URLs', () {
      // Simulate the swapSessionTokenIfNeeded function from auth_interceptor.dart
      String swapSessionTokenIfNeeded(String url, String newToken) {
        if (url.contains('/sessions/')) {
          RegExp sessionTokenRegExp = RegExp(r'/sessions/([^/]+)');
          return url.replaceFirstMapped(sessionTokenRegExp, (match) {
            return '/sessions/$newToken';
          });
        }
        return url;
      }

      const originalUrl = '/shim-service/organizations/00DHn0000019Y8b/sessions/oldToken123/actions/keep-alive';
      const newToken = 'newToken456';
      const expectedUrl = '/shim-service/organizations/00DHn0000019Y8b/sessions/newToken456/actions/keep-alive';

      final result = swapSessionTokenIfNeeded(originalUrl, newToken);
      expect(result, equals(expectedUrl));

      // Test with end session URL as well
      const endSessionUrl = '/shim-service/organizations/00DHn0000019Y8b/sessions/oldToken123';
      const expectedEndSessionUrl = '/shim-service/organizations/00DHn0000019Y8b/sessions/newToken456';

      final endSessionResult = swapSessionTokenIfNeeded(endSessionUrl, newToken);
      expect(endSessionResult, equals(expectedEndSessionUrl));
    });
  });
}
