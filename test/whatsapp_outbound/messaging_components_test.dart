import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/dtos/channel_messaging_definitions_response.dart';
import 'package:x1440/api/dtos/messaging_definition.dart';
import 'package:x1440/api/dtos/messaging_end_user_filters_response.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/api/shim_service_api.dart';
import 'package:x1440/frameworks/typing_indicator/typing_indicator_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/repositories/conversation/conversation_repository.dart';
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart';
import 'package:x1440/repositories/message_queue/message_queue_repository.dart';
import 'package:x1440/repositories/notifications/notifications_repository.dart';
import 'package:x1440/repositories/salesforce_data_repository/salesforce_data_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/repositories/websocket/messaging_definitions_repository.dart';
import 'package:x1440/repositories/websocket/messaging_definitions_repository_impl.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart';
import 'package:x1440/ui/blocs/chat/chat_bloc.dart';
import 'package:x1440/ui/blocs/chat/chat_event.dart';
import 'package:x1440/ui/blocs/chat/chat_state.dart';
import 'package:x1440/ui/blocs/messaging/definitions/messaging_definitions_bloc.dart';
import 'package:x1440/ui/blocs/messaging/definitions/messaging_definitions_event.dart';
import 'package:x1440/ui/blocs/messaging/definitions/messaging_definitions_state.dart';
import 'package:x1440/ui/screens/templates/template_selection_screen.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_definitions_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_exceptions.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/messaging/models/messaging_definition_status.dart';

class ShimServiceApiMock extends Mock implements ShimServiceApi {}

class RemoteLoggerMock extends Mock implements RemoteLogger {}

class ConversationRepositoryMock extends Mock
    implements ConversationRepository {}

class MessageQueueRepositoryMock extends Mock
    implements MessageQueueRepository {}

class ShimWebsocketRepositoryMock extends Mock
    implements ShimWebsocketRepository {}

class NotificationsRepositoryMock extends Mock
    implements NotificationsRepository {}

class SalesforceDataRepositoryMock extends Mock
    implements SalesforceDataRepository {}

class LocalStorageRepositoryMock extends Mock
    implements LocalStorageRepository {}

class AppLifeCycleRepositoryMock extends Mock
    implements AppLifeCycleRepository {}

class MessagingDefinitionsUseCaseMock extends Mock
    implements MessagingDefinitionsUseCase {}

class TypingIndicatorManagerMock extends Mock
    implements TypingIndicatorManager {}

class ContactsUseCaseMock extends Mock implements ContactsUseCase {}

class ConversationsUseCaseMock extends Mock implements ConversationsUseCase {}

class MessagingUseCaseMock extends Mock implements MessagingUseCase {}

class MessagingDefinitionsBlocMock
    extends MockBloc<MessagingDefinitionsEvent, MessagingDefinitionsState>
    implements MessagingDefinitionsBloc {}

class ChatBlocMock extends MockBloc<ChatEvent, ChatState> implements ChatBloc {}

void main() {
  late ShimServiceApiMock api;
  late RemoteLoggerMock remoteLogger;
  late MessagingDefinitionsRepository messagingRepository;
  late MessagingDefinitionsUseCase messagingUseCase;

  setUp(() async {
    registerFallbackValue(Uri());

    api = ShimServiceApiMock();
    remoteLogger = RemoteLoggerMock();
    messagingRepository = MessagingDefinitionsRepositoryImpl(api);

    messagingUseCase =
        MessagingDefinitionsUseCase(remoteLogger, messagingRepository);
  });

  group('Messaging Repository Tests', () {
    test('retrieves 2 filters', () async {
      // arrange
      when(() => api.getMessagingEndUserFilters("123456789ABC"))
          .thenAnswer((_) async => MessagingEndUserFiltersResponse.fromJson({
                "filters": [
                  {
                    "messageType": "TIK_TOK",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-980-7878",
                    "channelId": "123456789ABC",
                    "enforceMessagingComponent": true
                  },
                  {
                    "messageType": "WhatsApp",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-565-5030",
                    "channelId": "0MjHp000000wlHr",
                    "enforceMessagingComponent": true
                  }
                ]
              }));

      // act
      final response =
          await messagingRepository.getMessagingEndUserFilters("123456789ABC");

      // assert
      expect(response, isA<Success>());

      Success result = response as Success;
      MessagingEndUserFiltersResponse responseFilters = result.data;

      expect(responseFilters.filters.length, 2);
      expect(responseFilters.filters[0].messageType, "TIK_TOK");
      expect(responseFilters.filters[1].messageType, "WhatsApp");
      expect(responseFilters.filters[0].channelId, "123456789ABC");
      expect(responseFilters.filters[1].channelId, "0MjHp000000wlHr");
      expect(responseFilters.filters[0].enforceMessagingComponent, true);
      expect(responseFilters.filters[1].enforceMessagingComponent, true);

      verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);

      verifyNoMoreInteractions(api);
    });

    test(
      'Handle API Filters Error',
      () async {
        // arrange
        when(() => api.getMessagingEndUserFilters("123456789ABC")).thenThrow(
          DioException(
            response: Response(
              data: 'Something went wrong',
              statusCode: 400,
              requestOptions: RequestOptions(path: ''),
            ),
            requestOptions: RequestOptions(path: ''),
          ),
        );
        // act
        final response = await messagingRepository
            .getMessagingEndUserFilters("123456789ABC");

        expect(response, isA<Error>());
        Error error = response as Error;
        expect(error.error.statusCode, 400);

        verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);

        verifyNoMoreInteractions(api);
      },
    );

    test('retrieves 0 filter', () async {
      // arrange
      when(() => api.getMessagingEndUserFilters("123456789ABC")).thenAnswer(
          (_) async =>
              MessagingEndUserFiltersResponse.fromJson({"filters": []}));

      // act
      final response =
          await messagingRepository.getMessagingEndUserFilters("123456789ABC");

      // assert
      expect(response, isA<Success>());

      Success result = response as Success;
      MessagingEndUserFiltersResponse responseFilters = result.data;

      expect(responseFilters.filters.length, 0);
      expect(responseFilters.filters, isEmpty);

      verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);

      verifyNoMoreInteractions(api);
    });

    test('retrieves valid definitions', () async {
      // arrange
      when(() => api.getChannelMessagingDefinitions("channelId1234"))
          .thenAnswer(
              (_) async => ChannelMessagingDefinitionsResponse.fromJson({
                    "definitions": [
                      {
                        "id": "1mdHp000000wkfpIAA",
                        "name": "1440 Return Received (es)",
                        "hasRequiredParameters": false
                      },
                      {
                        "id": "1mdHp000000wkfoIAA",
                        "name": "Statement Available (en_US)",
                        "description":
                            "Notification that monthly rewards points account statement is available",
                        "hasRequiredParameters": false
                      }
                    ]
                  }));

      // act
      final response = await messagingRepository
          .getChannelMessagingDefinitions("channelId1234");

      // assert
      expect(response, isA<Success>());

      Success result = response as Success;
      ChannelMessagingDefinitionsResponse responseDefinitions = result.data;

      expect(responseDefinitions.definitions.length, 2);
      expect(responseDefinitions.definitions[0].id, "1mdHp000000wkfpIAA");
      expect(
          responseDefinitions.definitions[0].name, "1440 Return Received (es)");
      expect(responseDefinitions.definitions[0].description, '');
      expect(responseDefinitions.definitions[1].id, "1mdHp000000wkfoIAA");
      expect(responseDefinitions.definitions[1].name,
          "Statement Available (en_US)");
      expect(responseDefinitions.definitions[1].description,
          "Notification that monthly rewards points account statement is available");

      verify(() => api.getChannelMessagingDefinitions("channelId1234"))
          .called(1);

      verifyNoMoreInteractions(api);
    });

    test('retrieves no definitions', () async {
      // arrange
      when(() => api.getChannelMessagingDefinitions("channelId1234"))
          .thenAnswer((_) async => ChannelMessagingDefinitionsResponse.fromJson(
              {"definitions": []}));

      // act
      final response = await messagingRepository
          .getChannelMessagingDefinitions("channelId1234");

      // assert
      expect(response, isA<Success>());

      Success result = response as Success;
      ChannelMessagingDefinitionsResponse responseDefinitions = result.data;

      expect(responseDefinitions.definitions.length, 0);
      expect(responseDefinitions.definitions, isEmpty);

      verify(() => api.getChannelMessagingDefinitions("channelId1234"))
          .called(1);

      verifyNoMoreInteractions(api);
    });

    test(
      'Handle API definitions error',
      () async {
        // arrange
        when(() => api.getChannelMessagingDefinitions("channelId1234"))
            .thenThrow(
          DioException(
            response: Response(
              data: 'Something went wrong',
              statusCode: 403,
              requestOptions: RequestOptions(path: ''),
            ),
            requestOptions: RequestOptions(path: ''),
          ),
        );
        // act
        final response = await messagingRepository
            .getChannelMessagingDefinitions("channelId1234");

        expect(response, isA<Error>());
        Error error = response as Error;
        expect(error.error.statusCode, 403);

        verify(() => api.getChannelMessagingDefinitions("channelId1234"))
            .called(1);

        verifyNoMoreInteractions(api);
      },
    );
  });

  group('Messaging Use Case Tests', () {
    test('retrieves valid message definitions', () async {
      // arrange
      when(() => api.getMessagingEndUserFilters("123456789ABC"))
          .thenAnswer((_) async => MessagingEndUserFiltersResponse.fromJson({
                "filters": [
                  {
                    "messageType": "TIK_TOK",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-980-7878",
                    "channelId": "123456789ABC",
                    "enforceMessagingComponent": true
                  },
                  {
                    "messageType": "WhatsApp",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-565-5030",
                    "channelId": "0MjHp000000wlHr",
                    "enforceMessagingComponent": true
                  }
                ]
              }));

      when(() => api.getChannelMessagingDefinitions("0MjHp000000wlHr"))
          .thenAnswer(
              (_) async => ChannelMessagingDefinitionsResponse.fromJson({
                    "definitions": [
                      {
                        "id": "1mdHp000000wkfpIAA",
                        "name": "1440 Return Received (es)",
                        "hasRequiredParameters": false
                      },
                      {
                        "id": "1mdHp000000wkfoIAA",
                        "name": "Statement Available (en_US)",
                        "description":
                            "Notification that monthly rewards points account statement is available",
                        "hasRequiredParameters": false
                      }
                    ]
                  }));

      // act
      final definitionStatus = await messagingUseCase
          .getMessagingDefinitionStatus("123456789ABC", "WhatsApp");

      // assert
      expect(definitionStatus.mustUseDefinition, true);
      expect(definitionStatus.definitions, isNotNull);
      expect(definitionStatus.definitions!.length, 2);
      expect(definitionStatus.definitions![0].id, "1mdHp000000wkfpIAA");
      expect(
          definitionStatus.definitions![0].name, "1440 Return Received (es)");
      expect(definitionStatus.definitions![0].description, '');
      expect(definitionStatus.definitions![1].id, "1mdHp000000wkfoIAA");
      expect(
          definitionStatus.definitions![1].name, "Statement Available (en_US)");
      expect(definitionStatus.definitions![1].description,
          "Notification that monthly rewards points account statement is available");

      verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);
      verify(() => api.getChannelMessagingDefinitions("0MjHp000000wlHr"))
          .called(1);

      verifyNoMoreInteractions(api);
    });

    test('retrieves invalid messageType', () async {
      // arrange
      when(() => api.getMessagingEndUserFilters("123456789ABC"))
          .thenAnswer((_) async => MessagingEndUserFiltersResponse.fromJson({
                "filters": [
                  {
                    "messageType": "TIK_TOK",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-980-7878",
                    "channelId": "123456789ABC",
                    "enforceMessagingComponent": true
                  },
                  {
                    "messageType": "WhatsApp",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-565-5030",
                    "channelId": "0MjHp000000wlHr",
                    "enforceMessagingComponent": true
                  }
                ]
              }));

      try {
        await messagingUseCase.getMessagingDefinitionStatus(
            "123456789ABC", "SOME_RANDOM_MESSAGE_TYPE");
        fail('Expected MessagingComponentException was not thrown');
      } catch (e) {
        expect(e, isA<UndefinedMessagingTypeException>());
        expect(
          (e as UndefinedMessagingTypeException).type,
          equals('SOME_RANDOM_MESSAGE_TYPE'),
        );
      }

      verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);
      verifyNever(() => api.getChannelMessagingDefinitions(any()));

      verifyNoMoreInteractions(api);
    });

    test('retrieves invalid end message user id', () async {
      // TODO, clarify here how the API behaves
      // arrange
      when(() => api.getMessagingEndUserFilters("INVALID_USER_ID"))
          .thenThrow(DioException(
        requestOptions: RequestOptions(path: ''),
        response: Response(
          requestOptions: RequestOptions(path: ''),
          statusCode: 400,
          data: {'error': 'Bad Request', 'message': 'Invalid user ID'},
        ),
        type: DioExceptionType.badResponse,
      ));

      // Remove the when clause for getChannelMessagingDefinitions as it won't be called

      // act
      expect(
        () => messagingUseCase.getMessagingDefinitionStatus(
            "INVALID_USER_ID", "WhatsApp"),
        throwsA(isA<MessagingApiException>()),
      );

      // assert
      verify(() => api.getMessagingEndUserFilters("INVALID_USER_ID")).called(1);
      verifyNever(() => api.getChannelMessagingDefinitions(any()));

      verifyNoMoreInteractions(api);
    });

    test(
        'retrieves no definitions caused by enforceMessagingComponent being false',
        () async {
      // arrange
      when(() => api.getMessagingEndUserFilters("123456789ABC"))
          .thenAnswer((_) async => MessagingEndUserFiltersResponse.fromJson({
                "filters": [
                  {
                    "messageType": "TIK_TOK",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-980-7878",
                    "channelId": "123456789ABC",
                    "enforceMessagingComponent": false
                  },
                  {
                    "messageType": "WhatsApp",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-565-5030",
                    "channelId": "0MjHp000000wlHr",
                    "enforceMessagingComponent": false
                  }
                ]
              }));

      final definitionStatus = await messagingUseCase
          .getMessagingDefinitionStatus("123456789ABC", "WhatsApp");
      expect(definitionStatus.mustUseDefinition, false);
      expect(definitionStatus.definitions, isNull);

      verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);
      verifyNever(() => api.getChannelMessagingDefinitions("0MjHp000000wlHr"));

      verifyNoMoreInteractions(api);
    });

    test('retrieves no message definitions for valid messageType', () async {
      // arrange
      when(() => api.getMessagingEndUserFilters("123456789ABC"))
          .thenAnswer((_) async => MessagingEndUserFiltersResponse.fromJson({
                "filters": [
                  {
                    "messageType": "TIK_TOK",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-980-7878",
                    "channelId": "123456789ABC",
                    "enforceMessagingComponent": true
                  },
                  {
                    "messageType": "WhatsApp",
                    "consentStatus": "IMPLICITLY_OPTED_IN",
                    "channelName": "******-565-5030",
                    "channelId": "0MjHp000000wlHr",
                    "enforceMessagingComponent": true
                  }
                ]
              }));

      when(() => api.getChannelMessagingDefinitions("0MjHp000000wlHr"))
          .thenAnswer((_) async => ChannelMessagingDefinitionsResponse.fromJson(
              {"definitions": []}));

      // act
      final definitionStatus = await messagingUseCase
          .getMessagingDefinitionStatus("123456789ABC", "WhatsApp");

      // assert
      expect(definitionStatus.definitions, isNotNull);
      expect(definitionStatus.definitions, isEmpty);
      verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);
      verify(() => api.getChannelMessagingDefinitions(any())).called(1);

      verifyNoMoreInteractions(api);
    });

    test('retrieves no message definitions, no filters', () async {
      // arrange
      when(() => api.getMessagingEndUserFilters("123456789ABC")).thenAnswer(
          (_) async =>
              MessagingEndUserFiltersResponse.fromJson({"filters": []}));

      // assert
      try {
        await messagingUseCase.getMessagingDefinitionStatus(
            "123456789ABC", "WhatsApp");
        fail('Expected MessagingComponentException was not thrown');
      } catch (e) {
        expect(e, isA<NoFiltersDefinedException>());
        expect(
          (e as NoFiltersDefinedException).endMessagingUserId,
          equals('123456789ABC'),
        );
      }

      verify(() => api.getMessagingEndUserFilters("123456789ABC")).called(1);
      verifyNever(() => api.getChannelMessagingDefinitions(any()));

      verifyNoMoreInteractions(api);
    });

    // Add tests with Errors and check if logger being called
  });

  group(
    'Messaging Bloc Tests',
    () {
      final MessagingDefinitionsUseCaseMock messagingDefinitionsUseCaseMock =
          MessagingDefinitionsUseCaseMock();
      late MessagingDefinitionStatus messagingDefinitionStatus;
      final RemoteLoggerMock remoteLoggerMock = RemoteLoggerMock();
      final TypingIndicatorManagerMock typingIndicatorManagerMock =
          TypingIndicatorManagerMock();
      final ContactsUseCaseMock contactsUseCaseMock = ContactsUseCaseMock();
      final ConversationsUseCaseMock conversationsUseCaseMock =
          ConversationsUseCaseMock();
      final MessagingUseCaseMock messagingUseCaseMock = MessagingUseCaseMock();

      blocTest<ChatBloc, ChatState>(
          'emits a state that contains loaded definitions',
          setUp: () {
            final messagingDefinitionStatus = MessagingDefinitionStatus(
                mustUseDefinition: true,
                definitions: ChannelMessagingDefinitionsResponse.fromJson({
                  "definitions": [
                    {
                      "id": "1mdHp000000wkfpIAA",
                      "name": "1440 Return Received (es)",
                      "hasRequiredParameters": false
                    },
                    {
                      "id": "1mdHp000000wkfoIAA",
                      "name": "Statement Available (en_US)",
                      "description":
                          "Notification that monthly rewards points account statement is available",
                      "hasRequiredParameters": false
                    }
                  ]
                }).definitions);

            when(() => messagingDefinitionsUseCaseMock
                    .getMessagingDefinitionStatus("123456789ABC", "WhatsApp"))
                .thenAnswer((_) async => messagingDefinitionStatus);
          },
          act: (bloc) =>
              bloc.add(GetMessagingDefinitions('123456789ABC', 'WhatsApp')),
          build: () => ChatBloc(
                conversationsUseCaseMock,
                typingIndicatorManagerMock,
                contactsUseCaseMock,
                messagingUseCaseMock,
                messagingDefinitionsUseCaseMock,
                remoteLoggerMock,
              ),
          expect: () => [
                isA<ChatState>().having((state) => state.isLoadingDefinitions,
                    'isLoadingDefinitions', true),
                isA<ChatState>()
                    .having(
                        (state) => state
                            .messagingDefinitionStatus.definitions!.length,
                        'messagingDefinitionStatus',
                        2)
                    .having((state) => state.isLoadingDefinitions,
                        'isLoadingDefinitions', false)
              ],
          verify: (_) {
            verify(() => messagingDefinitionsUseCaseMock
                    .getMessagingDefinitionStatus("123456789ABC", "WhatsApp"))
                .called(1);
          });

      blocTest<ChatBloc, ChatState>(
          'emits a state that can directly go to chat for manual text input',
          setUp: () {
            const messagingDefinitionStatus = MessagingDefinitionStatus(
                mustUseDefinition: false, definitions: null);

            when(() => messagingDefinitionsUseCaseMock
                    .getMessagingDefinitionStatus("123456789ABC", "WhatsApp"))
                .thenAnswer((_) async => messagingDefinitionStatus);
          },
          act: (bloc) =>
              bloc.add(GetMessagingDefinitions('123456789ABC', 'WhatsApp')),
          build: () => ChatBloc(
                conversationsUseCaseMock,
                typingIndicatorManagerMock,
                contactsUseCaseMock,
                messagingUseCaseMock,
                messagingDefinitionsUseCaseMock,
                remoteLoggerMock,
              ),
          expect: () => [
                isA<ChatState>().having((state) => state.isLoadingDefinitions,
                    'isLoadingDefinitions', true),
                isA<ChatState>()
                    .having(
                        (state) => state.messagingDefinitionStatus.definitions,
                        'messagingDefinitionStatus',
                        isNull)
                    .having((state) => state.isLoadingDefinitions,
                        'isLoadingDefinitions', false)
              ],
          verify: (_) {
            verify(() => messagingDefinitionsUseCaseMock
                    .getMessagingDefinitionStatus("123456789ABC", "WhatsApp"))
                .called(1);
          });

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Select a non selected definition',
        seed: () => MessagingDefinitionsState(
          selectedMessagingDefinition: MessagingDefinition.fromJson({
            "id": "1mdHp000000wkfoIAA",
            "name": "Statement Available (en_US)",
            "description":
                "Notification that monthly rewards points account statement is available",
            "hasRequiredParameters": false
          }),
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) =>
            bloc.add(SelectMessagingDefinition(MessagingDefinition.fromJson({
          "id": "1mdHp000000wkfpIAA",
          "name": "1440 Return Received (es)",
          "hasRequiredParameters": false
        }))),
        build: () => MessagingDefinitionsBloc(messagingDefinitionsUseCaseMock),
        expect: () => [
          isA<MessagingDefinitionsState>()
              .having((state) => state.selectedMessagingDefinition,
                  'selectedMessagingDefinition', isNotNull)
              .having((state) => state.selectedMessagingDefinition!.id,
                  'selectedMessagingDefinition', "1mdHp000000wkfpIAA"),
        ],
      );

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Select an already selected definition',
        seed: () => MessagingDefinitionsState(
          selectedMessagingDefinition: MessagingDefinition.fromJson({
            "id": "1mdHp000000wkfpIAA",
            "name": "1440 Return Received (es)",
            "hasRequiredParameters": false
          }),
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) =>
            bloc.add(SelectMessagingDefinition(MessagingDefinition.fromJson({
          "id": "1mdHp000000wkfpIAA",
          "name": "1440 Return Received (es)",
          "hasRequiredParameters": false
        }))),
        build: () => MessagingDefinitionsBloc(messagingDefinitionsUseCaseMock),
        expect: () => [
          isA<MessagingDefinitionsState>().having(
              (state) => state.selectedMessagingDefinition,
              'selectedMessagingDefinition',
              isNull),
        ],
      );

      blocTest<ChatBloc, ChatState>('No filters defined',
          setUp: () {
            when(() => messagingDefinitionsUseCaseMock
                    .getMessagingDefinitionStatus("123456789ABC", "BOGUS_TYPE"))
                .thenThrow(
              NoFiltersDefinedException(
                "123456789ABC",
              ),
            );
          },
          act: (bloc) =>
              bloc.add(GetMessagingDefinitions('123456789ABC', 'BOGUS_TYPE')),
          build: () => ChatBloc(
                conversationsUseCaseMock,
                typingIndicatorManagerMock,
                contactsUseCaseMock,
                messagingUseCaseMock,
                messagingDefinitionsUseCaseMock,
                remoteLoggerMock,
              ),
          expect: () => [
                isA<ChatState>()
                    .having((state) => state.messageDefinitionFailure,
                        'messageDefinitionFailure', isNotNull)
                    .having(
                        (state) => state.messageDefinitionFailure?.consume(),
                        'messageDefinitionFailure',
                        isA<UndefinedMessagingTypeException>()),
              ],
          verify: (_) {
            verifyNever(() => messagingDefinitionsUseCaseMock
                .getMessagingDefinitionStatus("123456789ABC", "BOGUS_TYPE"));
          });

      blocTest<ChatBloc, ChatState>('Api error',
          setUp: () {
            when(() => messagingDefinitionsUseCaseMock
                    .getMessagingDefinitionStatus("123456789ABC", "WhatsApp"))
                .thenThrow(
              MessagingApiException(
                const ApiError(statusCode: 400, message: 'session gone'),
              ),
            );
          },
          act: (bloc) =>
              bloc.add(GetMessagingDefinitions('123456789ABC', 'WhatsApp')),
          build: () => ChatBloc(
                conversationsUseCaseMock,
                typingIndicatorManagerMock,
                contactsUseCaseMock,
                messagingUseCaseMock,
                messagingDefinitionsUseCaseMock,
                remoteLoggerMock,
              ),
          skip: 1,
          expect: () => [
                isA<ChatState>()
                    .having((state) => state.messageDefinitionFailure,
                        'messageDefinitionFailure', isNotNull)
                    .having(
                        (state) => state.messageDefinitionFailure?.consume(),
                        'messageDefinitionFailure',
                        isA<MessagingApiException>()),
              ],
          verify: (_) {
            verify(() => messagingDefinitionsUseCaseMock
                    .getMessagingDefinitionStatus("123456789ABC", "WhatsApp"))
                .called(1);
          });

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Send a selected definition',
        seed: () => MessagingDefinitionsState(
          selectedMessagingDefinition: MessagingDefinition.fromJson({
            "id": "1mdHp000000wkfpIAA",
            "name": "1440 Return Received (es)",
            "hasRequiredParameters": false
          }),
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) => bloc.add(SendMessagingDefinition()),
        build: () => MessagingDefinitionsBloc(messagingDefinitionsUseCaseMock),
        expect: () => [
          isA<MessagingDefinitionsState>()
              .having((state) => state.selectedMessagingDefinition,
                  'selectedMessagingDefinition', isNotNull)
              .having(
                  (state) => state.navigateToChat, 'navigateToChat', isNotNull)
        ],
      );

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Send a non selected definition',
        seed: () => MessagingDefinitionsState(
          selectedMessagingDefinition: null,
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) => bloc.add(SendMessagingDefinition()),
        build: () => MessagingDefinitionsBloc(messagingDefinitionsUseCaseMock),
        expect: () => [], // No emit was done
      );

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'init bloc and test last emit',
        setUp: () {
          messagingDefinitionStatus = MessagingDefinitionStatus(
              mustUseDefinition: true,
              definitions: ChannelMessagingDefinitionsResponse.fromJson({
                "definitions": [
                  {
                    "id": "1mdHp000000wkfpIAA",
                    "name": "1440 Return Received (es)",
                    "hasRequiredParameters": false
                  },
                  {
                    "id": "1mdHp000000wkfoIAA",
                    "name": "Statement Available (en_US)",
                    "description":
                        "Notification that monthly rewards points account statement is available",
                    "hasRequiredParameters": false
                  }
                ]
              }).definitions);
        },
        act: (bloc) => bloc.add(InitDefinitions(
            "123456789ABC", "WhatsApp", messagingDefinitionStatus)),
        build: () => MessagingDefinitionsBloc(messagingDefinitionsUseCaseMock),
        expect: () => [
          isA<MessagingDefinitionsState>()
              .having((state) => state.messagingEndUser, 'messagingEndUser',
                  "123456789ABC")
              .having(
                  (state) => state.messagingType, 'messagingType', "WhatsApp")
              .having(
                  (state) =>
                      state.messagingDefinitionStatus!.definitions!.length,
                  'messagingDefinitionStatus',
                  2)
        ],
      );

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Search existing term -> 1 definition',
        seed: () => MessagingDefinitionsState(
          messagingDefinitionStatus: MessagingDefinitionStatus(
            mustUseDefinition: true,
            definitions: ChannelMessagingDefinitionsResponse.fromJson({
              "definitions": [
                {
                  "id": "1mdHp000000wkfpIAA",
                  "name": "1440 Return Received (es)",
                  "hasRequiredParameters": false
                },
                {
                  "id": "1mdHp000000wkfoIAA",
                  "name": "Statement Available (en_US)",
                  "description":
                      "Notification that monthly rewards points account statement is available",
                  "hasRequiredParameters": false
                }
              ]
            }).definitions,
          ),
          selectedMessagingDefinition: null,
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) => bloc.add(SearchDefinitions("available")),
        build: () => MessagingDefinitionsBloc(
            MessagingDefinitionsUseCase(remoteLogger, messagingRepository)),
        expect: () => [
          isA<MessagingDefinitionsState>()
              .having(
                  (state) => state.filteredDefinitions?.length, 'not empty', 1)
              .having((state) => state.filteredDefinitions?[0].id,
                  'filteredDefinitions id', "1mdHp000000wkfoIAA")
        ],
      );

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Search existing term -> 2 definitions',
        seed: () => MessagingDefinitionsState(
          messagingDefinitionStatus: MessagingDefinitionStatus(
            mustUseDefinition: true,
            definitions: ChannelMessagingDefinitionsResponse.fromJson({
              "definitions": [
                {
                  "id": "1mdHp000000wkfpIAA",
                  "name": "1440 Return Received (es)",
                  "hasRequiredParameters": false
                },
                {
                  "id": "1mdHp000000wkfoIAA",
                  "name": "1440 Statement Available (en_US)",
                  "description":
                      "Notification that monthly rewards points account statement is available",
                  "hasRequiredParameters": false
                }
              ]
            }).definitions,
          ),
          selectedMessagingDefinition: null,
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) => bloc.add(SearchDefinitions("1440")),
        build: () => MessagingDefinitionsBloc(
            MessagingDefinitionsUseCase(remoteLogger, messagingRepository)),
        expect: () => [
          isA<MessagingDefinitionsState>()
              .having(
                  (state) => state.filteredDefinitions?.length, 'not empty', 2)
              .having((state) => state.filteredDefinitions?[0].id,
                  'filteredDefinitions id', "1mdHp000000wkfpIAA")
              .having((state) => state.filteredDefinitions?[1].id,
                  'filteredDefinitions id', "1mdHp000000wkfoIAA")
        ],
      );

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Search with a term < 3 characters should not yield any new state',
        seed: () => MessagingDefinitionsState(
          messagingDefinitionStatus: MessagingDefinitionStatus(
            mustUseDefinition: true,
            definitions: ChannelMessagingDefinitionsResponse.fromJson({
              "definitions": [
                {
                  "id": "1mdHp000000wkfpIAA",
                  "name": "1440 Return Received (es)",
                  "hasRequiredParameters": false
                },
                {
                  "id": "1mdHp000000wkfoIAA",
                  "name": "1440 Statement Available (en_US)",
                  "description":
                      "Notification that monthly rewards points account statement is available",
                  "hasRequiredParameters": false
                }
              ]
            }).definitions,
          ),
          selectedMessagingDefinition: null,
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) => bloc.add(SearchDefinitions("14")),
        build: () => MessagingDefinitionsBloc(
            MessagingDefinitionsUseCase(remoteLogger, messagingRepository)),
        expect: () => [], // No state is emitted
      );

// Test below blows up internally in the bloc test library, not sure why it does not like empty
      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Search non existing term -> 0 definition',
        seed: () => MessagingDefinitionsState(
          messagingDefinitionStatus: MessagingDefinitionStatus(
            mustUseDefinition: true,
            definitions: ChannelMessagingDefinitionsResponse.fromJson({
              "definitions": [
                {
                  "id": "1mdHp000000wkfpIAA",
                  "name": "1440 Return Received (es)",
                  "hasRequiredParameters": false
                },
                {
                  "id": "1mdHp000000wkfoIAA",
                  "name": "1440 Statement Available (en_US)",
                  "description":
                      "Notification that monthly rewards points account statement is available",
                  "hasRequiredParameters": false
                }
              ]
            }).definitions,
          ),
          selectedMessagingDefinition: null,
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) => bloc.add(SearchDefinitions("hello")),
        build: () => MessagingDefinitionsBloc(
            MessagingDefinitionsUseCase(remoteLogger, messagingRepository)),
        expect: () => [
          isA<MessagingDefinitionsState>()
              .having((state) => state.filteredDefinitions, 'empty', isEmpty)
        ],
      );

      blocTest<MessagingDefinitionsBloc, MessagingDefinitionsState>(
        'Search non existing term, search existing terms, search no terms',
        seed: () => MessagingDefinitionsState(
          messagingDefinitionStatus: MessagingDefinitionStatus(
            mustUseDefinition: true,
            definitions: ChannelMessagingDefinitionsResponse.fromJson({
              "definitions": [
                {
                  "id": "1mdHp000000wkfpIAA",
                  "name": "1440 Return Received (es)",
                  "hasRequiredParameters": false
                },
                {
                  "id": "1mdHp000000wkfoIAA",
                  "name": "1440 Statement Available (en_US)",
                  "description":
                      "Notification that monthly rewards points account statement is available",
                  "hasRequiredParameters": false
                }
              ]
            }).definitions,
          ),
          selectedMessagingDefinition: null,
          messagingEndUser: SfId("123456789ABC"),
          messagingType: MessagingChannelType.whatsApp,
        ),
        act: (bloc) {
          bloc.add(SearchDefinitions(
              "hello")); // Non existing search term -> empty array
          bloc.add(SearchDefinitions("Statement"));
          bloc.add(SearchDefinitions("1440"));
          bloc.add(SearchDefinitions("")); // Empty search term -> null
        },
        build: () => MessagingDefinitionsBloc(
            MessagingDefinitionsUseCase(remoteLogger, messagingRepository)),
        expect: () => [
          isA<MessagingDefinitionsState>()
              .having((state) => state.filteredDefinitions, 'empty', isEmpty),
          isA<MessagingDefinitionsState>().having(
              (state) => state.filteredDefinitions?.length, 'found 1', 1),
          isA<MessagingDefinitionsState>().having(
              (state) => state.filteredDefinitions?.length, 'found 2', 2),
          isA<MessagingDefinitionsState>().having(
              (state) => state.filteredDefinitions, 'found none', isNull)
        ],
      );
    },
  );

  group(
    'Widget Tests',
    () {
      late MessagingDefinitionStatus messagingDefinitionStatus;

      setUp(() {
        messagingDefinitionStatus = MessagingDefinitionStatus(
            mustUseDefinition: true,
            definitions: ChannelMessagingDefinitionsResponse.fromJson({
              "definitions": [
                {
                  "id": "1mdHp000000wkfpIAA",
                  "name": "1440 Return Received (es)",
                  "hasRequiredParameters": false
                },
                {
                  "id": "1mdHp000000wkfoIAA",
                  "name": "Statement Available (en_US)",
                  "description":
                      "Notification that monthly rewards points account statement is available",
                  "hasRequiredParameters": false
                }
              ]
            }).definitions);
        // This runs before each tes
      });

      tearDown(() {
        // This runs after each test
        GetIt.I.unregister<MessagingDefinitionsUseCase>();
        GetIt.I.unregister<MessagingDefinitionsBloc>();
      });

      testWidgets('TemplateSelectionScreen displays correctly',
          (WidgetTester tester) async {
        // Initialize mock MessagingDefinitionsUseCase
        final messagingDefinitionsBloc =
            MessagingDefinitionsBloc(messagingUseCase);

        GetIt.I
            .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

        // Register the MessagingDefinitionsBloc in GetIt
        GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
            messagingDefinitionsBloc);

        // messagingDefinitionsBloc
        //     .add(InitDefinitions("123456789ABC", "WhatsApp"));

        // Build our app and trigger a frame.
        await tester.pumpWidget(
          MaterialApp(
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
            home: TemplateSelectionScreen(
                "123456789ABC", "WhatsApp", messagingDefinitionStatus),
          ),
        );

        // Verify that the initial state is displayed correctly
        expect(
            find.text(S
                .of(tester.element(find.byType(TemplateSelectionScreen)))
                .templates_selection_title),
            findsOneWidget);

        // Wait for the widget to rebuild
        await tester.pumpAndSettle();

        final state = messagingDefinitionsBloc.state;

        expect(state.messagingDefinitionStatus, isNotNull);
        expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
        expect(state.messagingDefinitionStatus?.definitions?.length, 2);

        final containers = tester.widgetList<Container>(find.ancestor(
          of: find.byType(Row),
          matching: find.byType(Container),
        ));

        expect(containers.length, 2);
        expect(containers.elementAt(0).color, isNull);
        expect(containers.elementAt(1).color, isNull);

        final buttonFinder = find.byType(ElevatedButton);
        expect(buttonFinder, findsOneWidget);

        // Get the ElevatedButton widget
        final ElevatedButton button =
            tester.widget<ElevatedButton>(buttonFinder);

        // Check if onPressed is null
        expect(button.onPressed, isNull); // Nothing is selected
      });

      testWidgets('TemplateSelectionScreen with no templates',
          (WidgetTester tester) async {
        messagingDefinitionStatus = MessagingDefinitionStatus(
            mustUseDefinition: true,
            definitions: ChannelMessagingDefinitionsResponse.fromJson(
                {"definitions": []}).definitions);

        // Initialize mock MessagingDefinitionsUseCase
        final messagingDefinitionsBloc =
            MessagingDefinitionsBloc(messagingUseCase);

        GetIt.I
            .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

        // Register the MessagingDefinitionsBloc in GetIt
        GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
            messagingDefinitionsBloc);

        // Build our app and trigger a frame.
        await tester.pumpWidget(
          MaterialApp(
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
            home: TemplateSelectionScreen(
                "123456789ABC", "WhatsApp", messagingDefinitionStatus),
          ),
        );

        expect(
            find.text(S
                .of(tester.element(find.byType(TemplateSelectionScreen)))
                .templates_selection_title),
            findsOneWidget);

        // Wait async operations to complete
        await tester.pumpAndSettle();

        final state = messagingDefinitionsBloc.state;

        expect(state.messagingDefinitionStatus, isNotNull);
        expect(state.messagingDefinitionStatus?.definitions, isEmpty);

        expect(find.byType(SvgPicture), findsOneWidget);
        expect(
            find.text(S
                .of(tester.element(find.byType(TemplateSelectionScreen)))
                .no_templates_available_label),
            findsOneWidget);

        final buttonFinder = find.byType(ElevatedButton);

        // Verify that the button exists
        expect(buttonFinder, findsNothing);
      });

      testWidgets(
          'TemplateSelectionScreen with one selected template displays correctly',
          (WidgetTester tester) async {
        // Initialize mock MessagingDefinitionsUseCase
        final messagingDefinitionsBloc =
            MessagingDefinitionsBloc(messagingUseCase);

        GetIt.I
            .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

        // Register the MessagingDefinitionsBloc in GetIt
        GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
            messagingDefinitionsBloc);

        // Build our app and trigger a frame.
        await tester.pumpWidget(
          MaterialApp(
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
            home: TemplateSelectionScreen(
                "123456789ABC", "WhatsApp", messagingDefinitionStatus),
          ),
        );

        // Verify that the initial state is displayed correctly
        expect(
            find.text(S
                .of(tester.element(find.byType(TemplateSelectionScreen)))
                .templates_selection_title),
            findsOneWidget);

        messagingDefinitionsBloc
            .add(SelectMessagingDefinition(MessagingDefinition.fromJson({
          "id": "1mdHp000000wkfpIAA",
          "name": "1440 Return Received (es)",
          "hasRequiredParameters": false
        })));

        // Wait async operations to complete
        await tester.pumpAndSettle();

        final state = messagingDefinitionsBloc.state;

        expect(state.messagingDefinitionStatus, isNotNull);
        expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
        expect(state.messagingDefinitionStatus?.definitions?.length, 2);

        final containers = tester.widgetList<Container>(find.ancestor(
          of: find.byType(Row),
          matching: find.byType(Container),
        ));

        expect(containers.length, 2);
        expect(containers.elementAt(0).color, isNotNull); // Selected
        expect(containers.elementAt(1).color, isNull);

        final buttonFinder = find.byType(ElevatedButton);
        expect(buttonFinder, findsOneWidget);

        // Get the ElevatedButton widget
        final ElevatedButton button =
            tester.widget<ElevatedButton>(buttonFinder);

        // Check if onPressed is null
        expect(button.onPressed, isNotNull);
      });

      testWidgets(
          'TemplateSelectionScreen with one selected template that gets re-selecteddisplays correctly (aka Toggle behavior)',
          (WidgetTester tester) async {
        // Initialize mock MessagingDefinitionsUseCase
        final messagingDefinitionsBloc =
            MessagingDefinitionsBloc(messagingUseCase);

        GetIt.I
            .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

        // Register the MessagingDefinitionsBloc in GetIt
        GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
            messagingDefinitionsBloc);

        messagingDefinitionsBloc.add(InitDefinitions(
            "123456789ABC", "WhatsApp", messagingDefinitionStatus));

        // Build our app and trigger a frame.
        await tester.pumpWidget(
          MaterialApp(
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
            home: TemplateSelectionScreen(
                "123456789ABC", "WhatsApp", messagingDefinitionStatus),
          ),
        );

        // Verify that the initial state is displayed correctly
        expect(
            find.text(S
                .of(tester.element(find.byType(TemplateSelectionScreen)))
                .templates_selection_title),
            findsOneWidget);

        // Wait async operations to complete
        await tester.pumpAndSettle();

        messagingDefinitionsBloc
            .add(SelectMessagingDefinition(MessagingDefinition.fromJson({
          "id": "1mdHp000000wkfpIAA",
          "name": "1440 Return Received (es)",
          "hasRequiredParameters": false
        })));

        // Wait async operations to complete
        await tester.pumpAndSettle();

        final state = messagingDefinitionsBloc.state;

        expect(state.messagingDefinitionStatus, isNotNull);
        expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
        expect(state.messagingDefinitionStatus?.definitions?.length, 2);

        var containers = tester.widgetList<Container>(find.ancestor(
          of: find.byType(Row),
          matching: find.byType(Container),
        ));

        expect(containers.length, 2);
        expect(containers.elementAt(0).color, isNotNull); // Selected
        expect(containers.elementAt(1).color, isNull);

        Finder buttonFinder = find.byType(ElevatedButton);
        expect(buttonFinder, findsOneWidget);

        // Get the ElevatedButton widget
        ElevatedButton button = tester.widget<ElevatedButton>(buttonFinder);

        // Check if onPressed is null
        expect(button.onPressed, isNotNull);

        messagingDefinitionsBloc
            .add(SelectMessagingDefinition(MessagingDefinition.fromJson({
          "id": "1mdHp000000wkfpIAA",
          "name": "1440 Return Received (es)",
          "hasRequiredParameters": false
        })));

        // Wait async operations to complete
        await tester.pumpAndSettle();

        containers = tester.widgetList<Container>(find.ancestor(
          of: find.byType(Row),
          matching: find.byType(Container),
        ));

        expect(containers.length, 2);
        expect(containers.elementAt(0).color, isNull); // Selected
        expect(containers.elementAt(1).color, isNull);

        expect(buttonFinder, findsOneWidget);
        // Check if onPressed is null
        buttonFinder = find.byType(ElevatedButton);
        expect(buttonFinder, findsOneWidget);

        // Get the ElevatedButton widget
        button = tester.widget<ElevatedButton>(buttonFinder);

        // Check if onPressed is null
        expect(button.onPressed, isNull);
      });

      testWidgets(
        'TemplateSelectionScreen with a search term that returns one entry out of the 2',
        (WidgetTester tester) async {
          // Initialize mock MessagingDefinitionsUseCase
          final messagingDefinitionsBloc =
              MessagingDefinitionsBloc(messagingUseCase);

          GetIt.I
              .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

          // Register the MessagingDefinitionsBloc in GetIt
          GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
              messagingDefinitionsBloc);

          messagingDefinitionsBloc.add(InitDefinitions(
              "123456789ABC", "WhatsApp", messagingDefinitionStatus));

          // Build our app and trigger a frame.
          await tester.pumpWidget(
            MaterialApp(
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              home: TemplateSelectionScreen(
                  "123456789ABC", "WhatsApp", messagingDefinitionStatus),
            ),
          );

          // Verify that the initial state is displayed correctly
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .templates_selection_title),
              findsOneWidget);

          final state = messagingDefinitionsBloc.state;

          expect(state.messagingDefinitionStatus, isNotNull);
          expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
          expect(state.messagingDefinitionStatus?.definitions?.length, 2);

          var containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNull); // Selected
          expect(containers.elementAt(1).color, isNull);

          final buttonFinder = find.byType(ElevatedButton);
          expect(buttonFinder, findsOneWidget);

          // Get the ElevatedButton widget
          final ElevatedButton button =
              tester.widget<ElevatedButton>(buttonFinder);

          // Check if onPressed is null
          expect(button.onPressed, isNull);

          messagingDefinitionsBloc.add(SearchDefinitions("Statement"));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 1);
        },
      );

      testWidgets(
        'TemplateSelectionScreen with a search term that has a length < 3 should not yield any changes',
        (WidgetTester tester) async {
          // Initialize mock MessagingDefinitionsUseCase
          final messagingDefinitionsBloc =
              MessagingDefinitionsBloc(messagingUseCase);

          GetIt.I
              .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

          // Register the MessagingDefinitionsBloc in GetIt
          GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
              messagingDefinitionsBloc);

          messagingDefinitionsBloc.add(InitDefinitions(
              "123456789ABC", "WhatsApp", messagingDefinitionStatus));

          // Build our app and trigger a frame.
          await tester.pumpWidget(
            MaterialApp(
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              home: TemplateSelectionScreen(
                  "123456789ABC", "WhatsApp", messagingDefinitionStatus),
            ),
          );

          // Verify that the initial state is displayed correctly
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .templates_selection_title),
              findsOneWidget);

          final state = messagingDefinitionsBloc.state;

          expect(state.messagingDefinitionStatus, isNotNull);
          expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
          expect(state.messagingDefinitionStatus?.definitions?.length, 2);

          var containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNull); // Selected
          expect(containers.elementAt(1).color, isNull);

          final buttonFinder = find.byType(ElevatedButton);
          expect(buttonFinder, findsOneWidget);

          // Get the ElevatedButton widget
          final ElevatedButton button =
              tester.widget<ElevatedButton>(buttonFinder);

          // Check if onPressed is null
          expect(button.onPressed, isNull);

          messagingDefinitionsBloc.add(SearchDefinitions("St"));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
        },
      );

      testWidgets(
        'TemplateSelectionScreen with a non existing search term that returns no entry ',
        (WidgetTester tester) async {
          // Initialize mock MessagingDefinitionsUseCase
          final messagingDefinitionsBloc =
              MessagingDefinitionsBloc(messagingUseCase);

          GetIt.I
              .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

          // Register the MessagingDefinitionsBloc in GetIt
          GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
              messagingDefinitionsBloc);

          messagingDefinitionsBloc.add(InitDefinitions(
              "123456789ABC", "WhatsApp", messagingDefinitionStatus));

          // Build our app and trigger a frame.
          await tester.pumpWidget(
            MaterialApp(
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              home: TemplateSelectionScreen(
                  "123456789ABC", "WhatsApp", messagingDefinitionStatus),
            ),
          );

          // Verify that the initial state is displayed correctly
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .templates_selection_title),
              findsOneWidget);

          final state = messagingDefinitionsBloc.state;

          expect(state.messagingDefinitionStatus, isNotNull);
          expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
          expect(state.messagingDefinitionStatus?.definitions?.length, 2);

          var containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNull); // Selected
          expect(containers.elementAt(1).color, isNull);

          var buttonFinder = find.byType(ElevatedButton);
          expect(buttonFinder, findsOneWidget);

          // Get the ElevatedButton widget
          final ElevatedButton button =
              tester.widget<ElevatedButton>(buttonFinder);

          // Check if onPressed is null
          expect(button.onPressed, isNull);

          messagingDefinitionsBloc.add(SearchDefinitions("some dumb entries"));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          expect(find.byType(SvgPicture), findsOneWidget);
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .no_templates_available_label),
              findsOneWidget);

          buttonFinder = find.byType(ElevatedButton);

          // Verify that the button exists
          expect(buttonFinder, findsNothing);
        },
      );

      testWidgets(
        'TemplateSelectionScreen with an empty search term that returns the original list',
        (WidgetTester tester) async {
          // Initialize mock MessagingDefinitionsUseCase
          final messagingDefinitionsBloc =
              MessagingDefinitionsBloc(messagingUseCase);

          GetIt.I
              .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

          // Register the MessagingDefinitionsBloc in GetIt
          GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
              messagingDefinitionsBloc);

          messagingDefinitionsBloc.add(InitDefinitions(
              "123456789ABC", "WhatsApp", messagingDefinitionStatus));

          // Build our app and trigger a frame.
          await tester.pumpWidget(
            MaterialApp(
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              home: TemplateSelectionScreen(
                  "123456789ABC", "WhatsApp", messagingDefinitionStatus),
            ),
          );

          // Verify that the initial state is displayed correctly
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .templates_selection_title),
              findsOneWidget);

          final state = messagingDefinitionsBloc.state;

          expect(state.messagingDefinitionStatus, isNotNull);
          expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
          expect(state.messagingDefinitionStatus?.definitions?.length, 2);

          var containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNull); // Selected
          expect(containers.elementAt(1).color, isNull);

          var buttonFinder = find.byType(ElevatedButton);
          expect(buttonFinder, findsOneWidget);

          // Get the ElevatedButton widget
          final ElevatedButton button =
              tester.widget<ElevatedButton>(buttonFinder);

          // Check if onPressed is null
          expect(button.onPressed, isNull);

          messagingDefinitionsBloc.add(SearchDefinitions("Statement"));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 1);

          messagingDefinitionsBloc.add(SearchDefinitions(""));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNull); // Selected
          expect(containers.elementAt(1).color, isNull);
        },
      );

      testWidgets(
        'TemplateSelectionScreen with the first item selected and checking that the item is selected in a filtered list',
        (WidgetTester tester) async {
          // Initialize mock MessagingDefinitionsUseCase
          final messagingDefinitionsBloc =
              MessagingDefinitionsBloc(messagingUseCase);

          GetIt.I
              .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

          // Register the MessagingDefinitionsBloc in GetIt
          GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
              messagingDefinitionsBloc);

          messagingDefinitionsBloc.add(InitDefinitions(
              "123456789ABC", "WhatsApp", messagingDefinitionStatus));

          // Build our app and trigger a frame.
          await tester.pumpWidget(
            MaterialApp(
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              home: TemplateSelectionScreen(
                  "123456789ABC", "WhatsApp", messagingDefinitionStatus),
            ),
          );

          // Verify that the initial state is displayed correctly
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .templates_selection_title),
              findsOneWidget);

          messagingDefinitionsBloc
              .add(SelectMessagingDefinition(MessagingDefinition.fromJson({
            "id": "1mdHp000000wkfpIAA",
            "name": "1440 Return Received (es)",
            "hasRequiredParameters": false
          })));

          await tester.pumpAndSettle();

          final state = messagingDefinitionsBloc.state;

          expect(state.messagingDefinitionStatus, isNotNull);
          expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
          expect(state.messagingDefinitionStatus?.definitions?.length, 2);

          var containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNotNull); // Selected
          expect(containers.elementAt(1).color, isNull);

          var buttonFinder = find.byType(ElevatedButton);
          expect(buttonFinder, findsOneWidget);

          // Get the ElevatedButton widget
          ElevatedButton button = tester.widget<ElevatedButton>(buttonFinder);

          // Check if onPressed is null
          expect(button.onPressed, isNotNull);

          messagingDefinitionsBloc.add(SearchDefinitions("Received"));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 1);
          expect(containers.elementAt(0).color, isNotNull); // Selected

          button = tester.widget<ElevatedButton>(buttonFinder);
          expect(button.onPressed, isNotNull);
        },
      );

      testWidgets(
        'TemplateSelectionScreen with the first item selected and checking that the item is not selected in a filtered list',
        (WidgetTester tester) async {
          // Initialize mock MessagingDefinitionsUseCase
          final messagingDefinitionsBloc =
              MessagingDefinitionsBloc(messagingUseCase);

          GetIt.I
              .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

          // Register the MessagingDefinitionsBloc in GetIt
          GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
              messagingDefinitionsBloc);

          messagingDefinitionsBloc.add(InitDefinitions(
              "123456789ABC", "WhatsApp", messagingDefinitionStatus));

          messagingDefinitionsBloc
              .add(SelectMessagingDefinition(MessagingDefinition.fromJson({
            "id": "1mdHp000000wkfoIAA",
            "name": "Statement Available (en_US)",
            "description":
                "Notification that monthly rewards points account statement is available",
            "hasRequiredParameters": false
          })));

          // Build our app and trigger a frame.
          await tester.pumpWidget(
            MaterialApp(
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              home: TemplateSelectionScreen(
                  "123456789ABC", "WhatsApp", messagingDefinitionStatus),
            ),
          );

          // Verify that the initial state is displayed correctly
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .templates_selection_title),
              findsOneWidget);

          final state = messagingDefinitionsBloc.state;

          expect(state.messagingDefinitionStatus, isNotNull);
          expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
          expect(state.messagingDefinitionStatus?.definitions?.length, 2);

          var containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNull); // Selected
          expect(containers.elementAt(1).color, isNotNull);

          var buttonFinder = find.byType(ElevatedButton);
          expect(buttonFinder, findsOneWidget);

          // Get the ElevatedButton widget
          ElevatedButton button = tester.widget<ElevatedButton>(buttonFinder);

          // Check if onPressed is null
          expect(button.onPressed, isNotNull);

          messagingDefinitionsBloc.add(SearchDefinitions("Received"));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 1);
          expect(containers.elementAt(0).color, isNull); // Not Selected

          button = tester.widget<ElevatedButton>(buttonFinder);
          expect(button.onPressed,
              isNull); // The button should be disabled as the selected element is not in the displayed list
        },
      );

      testWidgets(
        'TemplateSelectionScreen with the first item selected and checking that the item is not selected in a filtered list, then search for other item and check that the item is selected',
        (WidgetTester tester) async {
          // Initialize mock MessagingDefinitionsUseCase
          final messagingDefinitionsBloc =
              MessagingDefinitionsBloc(messagingUseCase);

          GetIt.I
              .registerSingleton<MessagingDefinitionsUseCase>(messagingUseCase);

          // Register the MessagingDefinitionsBloc in GetIt
          GetIt.I.registerSingleton<MessagingDefinitionsBloc>(
              messagingDefinitionsBloc);

          // Build our app and trigger a frame.
          await tester.pumpWidget(
            MaterialApp(
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              home: TemplateSelectionScreen(
                  "123456789ABC", "WhatsApp", messagingDefinitionStatus),
            ),
          );

          // Verify that the initial state is displayed correctly
          expect(
              find.text(S
                  .of(tester.element(find.byType(TemplateSelectionScreen)))
                  .templates_selection_title),
              findsOneWidget);

          messagingDefinitionsBloc
              .add(SelectMessagingDefinition(MessagingDefinition.fromJson({
            "id": "1mdHp000000wkfoIAA",
            "name": "Statement Available (en_US)",
            "description":
                "Notification that monthly rewards points account statement is available",
            "hasRequiredParameters": false
          })));

          await tester.pumpAndSettle();

          final state = messagingDefinitionsBloc.state;

          expect(state.messagingDefinitionStatus, isNotNull);
          expect(state.messagingDefinitionStatus?.definitions, isNotEmpty);
          expect(state.messagingDefinitionStatus?.definitions?.length, 2);

          var containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 2);
          expect(containers.elementAt(0).color, isNull); // Selected
          expect(containers.elementAt(1).color, isNotNull);

          var buttonFinder = find.byType(ElevatedButton);
          expect(buttonFinder, findsOneWidget);

          // Get the ElevatedButton widget
          ElevatedButton button = tester.widget<ElevatedButton>(buttonFinder);

          // Check if onPressed is null
          expect(button.onPressed, isNotNull);

          messagingDefinitionsBloc.add(SearchDefinitions("Received"));

          // Wait async operations to complete
          await tester.pumpAndSettle();

          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 1);
          expect(containers.elementAt(0).color, isNull); // Not Selected

          button = tester.widget<ElevatedButton>(buttonFinder);
          expect(button.onPressed,
              isNull); // The button should be disabled as the selected element is not in the displayed list

          messagingDefinitionsBloc.add(SearchDefinitions("Available"));

          // Wait async operations to complete
          await tester.pumpAndSettle();
          containers = tester.widgetList<Container>(find.ancestor(
            of: find.byType(Row),
            matching: find.byType(Container),
          ));

          expect(containers.length, 1);
          expect(containers.elementAt(0).color, isNotNull); //  Selected
          button = tester.widget<ElevatedButton>(buttonFinder);
          expect(button.onPressed, isNotNull);
        },
      );
    },
  );
}
